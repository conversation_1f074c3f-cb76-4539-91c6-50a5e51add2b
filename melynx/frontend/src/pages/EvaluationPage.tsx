import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Download, 
  ArrowLeft,
  Clock,
  User,
  Calendar
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Evaluation {
  id: string;
  filename: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  analysis?: {
    compliance_score: number;
    total_items: number;
    compliant_items: number;
    non_compliant_items: number;
    warnings: number;
    critical_issues: number;
    findings: Array<{
      type: 'compliant' | 'non_compliant' | 'warning' | 'critical';
      title: string;
      description: string;
      page_number?: number;
      recommendation?: string;
    }>;
  };
  page_count?: number;
  processed_pages?: number;
}

const EvaluationPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [evaluation, setEvaluation] = useState<Evaluation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('No evaluation ID provided');
      setLoading(false);
      return;
    }

    fetchEvaluation();
    
    // Poll for updates if evaluation is still processing
    const interval = setInterval(() => {
      if (evaluation?.status === 'processing' || evaluation?.status === 'pending') {
        fetchEvaluation();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [id, evaluation?.status]);

  const fetchEvaluation = async () => {
    try {
      const response = await fetch(`http://localhost:3001/api/evaluations/${id}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch evaluation: ${response.statusText}`);
      }

      const data = await response.json();
      setEvaluation(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching evaluation:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch evaluation');
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = async () => {
    try {
      const response = await fetch(`http://localhost:3001/api/evaluations/${id}/report`);
      
      if (!response.ok) {
        throw new Error('Failed to download report');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `MEL_Report_${evaluation?.filename || 'document'}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Report downloaded successfully');
    } catch (err) {
      console.error('Error downloading report:', err);
      toast.error('Failed to download report');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'processing':
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600 animate-pulse" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Analysis Complete';
      case 'processing':
        return 'Processing Document...';
      case 'pending':
        return 'Queued for Processing';
      case 'failed':
        return 'Analysis Failed';
      default:
        return 'Unknown Status';
    }
  };

  const getFindingIcon = (type: string) => {
    switch (type) {
      case 'compliant':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'non_compliant':
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getFindingBadgeClass = (type: string) => {
    switch (type) {
      case 'compliant':
        return 'status-badge status-success';
      case 'warning':
        return 'status-badge status-warning';
      case 'non_compliant':
      case 'critical':
        return 'status-badge status-danger';
      default:
        return 'status-badge status-info';
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <Clock className="h-12 w-12 text-aviation-blue mx-auto animate-pulse mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading evaluation...</h2>
          <p className="text-gray-600">Please wait while we fetch the evaluation details.</p>
        </div>
      </div>
    );
  }

  if (error || !evaluation) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Evaluation</h2>
          <p className="text-gray-600 mb-4">{error || 'Evaluation not found'}</p>
          <button
            onClick={() => navigate('/upload')}
            className="btn-primary"
          >
            Upload New Document
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/')}
            className="btn-secondary mr-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">MEL Evaluation</h1>
            <p className="text-gray-600">{evaluation.filename}</p>
          </div>
        </div>
        
        {evaluation.status === 'completed' && (
          <button
            onClick={downloadReport}
            className="btn-primary"
          >
            <Download className="h-4 w-4 mr-2" />
            Download Report
          </button>
        )}
      </div>

      {/* Status Card */}
      <div className="card mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {getStatusIcon(evaluation.status)}
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-gray-900">
                {getStatusText(evaluation.status)}
              </h3>
              <div className="flex items-center text-sm text-gray-600 mt-1">
                <Calendar className="h-4 w-4 mr-1" />
                <span>Started: {new Date(evaluation.created_at).toLocaleString()}</span>
                {evaluation.completed_at && (
                  <>
                    <span className="mx-2">•</span>
                    <span>Completed: {new Date(evaluation.completed_at).toLocaleString()}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          
          {evaluation.page_count && (
            <div className="text-right">
              <div className="text-sm text-gray-600">Document Pages</div>
              <div className="text-lg font-semibold text-gray-900">
                {evaluation.processed_pages || evaluation.page_count} / {evaluation.page_count}
              </div>
              {evaluation.processed_pages && evaluation.processed_pages < evaluation.page_count && (
                <div className="text-xs text-amber-600">Limited processing enabled</div>
              )}
            </div>
          )}
        </div>
      </div>

      {evaluation.status === 'processing' || evaluation.status === 'pending' ? (
        <div className="card text-center">
          <Clock className="h-16 w-16 text-aviation-blue mx-auto mb-4 animate-pulse" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {evaluation.status === 'pending' ? 'Queued for Processing' : 'Analyzing Document'}
          </h3>
          <p className="text-gray-600 mb-4">
            {evaluation.status === 'pending' 
              ? 'Your document is in the queue and will be processed shortly.'
              : 'Our AI is analyzing your MEL document for compliance issues. This may take a few minutes.'
            }
          </p>
          <div className="text-sm text-gray-500">
            This page will automatically update when the analysis is complete.
          </div>
        </div>
      ) : evaluation.status === 'failed' ? (
        <div className="card text-center">
          <XCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Analysis Failed</h3>
          <p className="text-gray-600 mb-4">
            We encountered an error while analyzing your document. Please try uploading again.
          </p>
          <button
            onClick={() => navigate('/upload')}
            className="btn-primary"
          >
            Upload New Document
          </button>
        </div>
      ) : evaluation.analysis ? (
        <>
          {/* Analysis Summary */}
          <div className="grid md:grid-cols-4 gap-6 mb-8">
            <div className="card text-center">
              <div className="text-3xl font-bold text-aviation-blue mb-2">
                {evaluation.analysis.compliance_score}%
              </div>
              <div className="text-gray-600">Compliance Score</div>
            </div>
            <div className="card text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {evaluation.analysis.compliant_items}
              </div>
              <div className="text-gray-600">Compliant Items</div>
            </div>
            <div className="card text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-2">
                {evaluation.analysis.warnings}
              </div>
              <div className="text-gray-600">Warnings</div>
            </div>
            <div className="card text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">
                {evaluation.analysis.critical_issues}
              </div>
              <div className="text-gray-600">Critical Issues</div>
            </div>
          </div>

          {/* Findings */}
          <div className="card">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Analysis Findings</h3>
            
            {evaluation.analysis.findings.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 mb-2">No Issues Found</h4>
                <p className="text-gray-600">Your MEL document appears to be fully compliant.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {evaluation.analysis.findings.map((finding, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start">
                      {getFindingIcon(finding.type)}
                      <div className="ml-3 flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-gray-900">{finding.title}</h4>
                          <div className="flex items-center space-x-2">
                            <span className={getFindingBadgeClass(finding.type)}>
                              {finding.type.replace('_', ' ').toUpperCase()}
                            </span>
                            {finding.page_number && (
                              <span className="text-xs text-gray-500">
                                Page {finding.page_number}
                              </span>
                            )}
                          </div>
                        </div>
                        <p className="text-gray-700 mb-2">{finding.description}</p>
                        {finding.recommendation && (
                          <div className="bg-blue-50 border border-blue-200 rounded p-3">
                            <h5 className="font-medium text-blue-900 mb-1">Recommendation:</h5>
                            <p className="text-blue-800 text-sm">{finding.recommendation}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </>
      ) : null}
    </div>
  );
};

export default EvaluationPage;
