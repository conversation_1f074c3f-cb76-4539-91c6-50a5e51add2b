import React from 'react';
import { Link } from 'react-router-dom';
import { Upload, FileText, BarChart3, Shield, Zap, CheckCircle } from 'lucide-react';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: Shield,
      title: 'AI-Powered Analysis',
      description: 'Advanced AI analyzes MEL documents for compliance and safety issues'
    },
    {
      icon: Zap,
      title: 'Fast Processing',
      description: 'Get results in seconds, not hours. Handles large documents efficiently'
    },
    {
      icon: CheckCircle,
      title: 'Comprehensive Reports',
      description: 'Detailed compliance reports with actionable recommendations'
    }
  ];

  return (
    <div className="px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          MEL Compliance Inspector
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          AI-powered Minimum Equipment List (MEL) compliance inspection for aviation safety.
          Upload your MEL documents and get instant compliance analysis.
        </p>
        <div className="flex justify-center space-x-4">
          <Link
            to="/upload"
            className="btn-primary inline-flex items-center"
          >
            <Upload className="h-5 w-5 mr-2" />
            Upload MEL Document
          </Link>
          <Link
            to="/reports"
            className="btn-secondary inline-flex items-center"
          >
            <BarChart3 className="h-5 w-5 mr-2" />
            View Reports
          </Link>
        </div>
      </div>

      {/* Features Section */}
      <div className="grid md:grid-cols-3 gap-8 mb-12">
        {features.map((feature, index) => {
          const Icon = feature.icon;
          return (
            <div key={index} className="card text-center">
              <Icon className="h-12 w-12 text-aviation-blue mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          );
        })}
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          System Status
        </h2>
        <div className="grid md:grid-cols-3 gap-6 text-center">
          <div>
            <div className="text-3xl font-bold text-aviation-blue mb-2">99.9%</div>
            <div className="text-gray-600">Accuracy Rate</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-green-600 mb-2">&lt;30s</div>
            <div className="text-gray-600">Average Processing Time</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
            <div className="text-gray-600">System Availability</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;