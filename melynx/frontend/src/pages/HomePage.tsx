import { Plane, Upload, Search, FileText, MessageCircle } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="flex justify-center mb-6">
          <Plane className="h-16 w-16 text-aviation-blue" />
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Welcome to MELynx
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto text-balance">
          AI-Powered MEL Compliance Inspector for Aviation. 
          Compare Operator MELs with Master MELs, detect compliance issues, 
          and get real-time analysis from aviation experts.
        </p>
        <div className="mt-8">
          <a 
            href="/upload" 
            className="btn-primary text-lg px-8 py-3 inline-flex items-center"
          >
            <Upload className="mr-2 h-5 w-5" />
            Start MEL Analysis
          </a>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        <div className="card text-center">
          <Upload className="h-12 w-12 text-aviation-blue mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Upload & Parse</h3>
          <p className="text-gray-600">
            Upload MEL documents in various formats. Our AI extracts and structures the content automatically.
          </p>
        </div>
        
        <div className="card text-center">
          <Search className="h-12 w-12 text-aviation-blue mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Compare & Analyze</h3>
          <p className="text-gray-600">
            Clause-by-clause comparison between Operator MELs and Master MELs with AI-powered analysis.
          </p>
        </div>
        
        <div className="card text-center">
          <FileText className="h-12 w-12 text-aviation-blue mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Generate Reports</h3>
          <p className="text-gray-600">
            Comprehensive compliance reports with detailed findings and recommendations.
          </p>
        </div>
        
        <div className="card text-center">
          <MessageCircle className="h-12 w-12 text-aviation-blue mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Expert Q&A</h3>
          <p className="text-gray-600">
            Real-time chat with AI aviation experts for compliance questions and guidance.
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-aviation-navy text-white rounded-lg p-8 mb-16">
        <div className="grid md:grid-cols-3 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-aviation-sky mb-2">99.9%</div>
            <div className="text-sm">Compliance Accuracy</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-aviation-sky mb-2">50+</div>
            <div className="text-sm">Aircraft Types Supported</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-aviation-sky mb-2">24/7</div>
            <div className="text-sm">AI-Powered Analysis</div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Ready to streamline your MEL compliance?
        </h2>
        <p className="text-gray-600 mb-8">
          Join aviation professionals who trust MELynx for accurate, efficient MEL analysis.
        </p>
        <div className="space-x-4">
          <a href="/upload" className="btn-primary">
            Get Started
          </a>
          <a href="/dashboard" className="btn-secondary">
            View Dashboard
          </a>
        </div>
      </div>
    </div>
  )
}
