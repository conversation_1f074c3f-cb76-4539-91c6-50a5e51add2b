import { BarChart3, FileText, Al<PERSON><PERSON>riangle, CheckCircle, Clock, Users } from 'lucide-react'

export default function DashboardPage() {
  // Mock data for demonstration
  const recentAnalyses = [
    { id: 1, aircraft: 'Boeing 737-800', status: 'completed', issues: 3, date: '2024-01-15' },
    { id: 2, aircraft: 'Airbus A320', status: 'in-progress', issues: 0, date: '2024-01-15' },
    { id: 3, aircraft: 'Boeing 777-300ER', status: 'completed', issues: 1, date: '2024-01-14' },
  ]

  const stats = {
    totalAnalyses: 47,
    activeInspectors: 12,
    complianceRate: 94.2,
    avgProcessingTime: '3.2 min'
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          MEL Compliance Dashboard
        </h1>
        <p className="text-lg text-gray-600">
          Monitor MEL analyses, compliance status, and inspector activities
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="card">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Analyses</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalAnalyses}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Inspectors</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeInspectors}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 rounded-lg">
              <BarChart3 className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Compliance Rate</p>
              <p className="text-2xl font-bold text-gray-900">{stats.complianceRate}%</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Processing</p>
              <p className="text-2xl font-bold text-gray-900">{stats.avgProcessingTime}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Analyses */}
      <div className="grid lg:grid-cols-2 gap-8">
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Recent MEL Analyses</h3>
          <div className="space-y-4">
            {recentAnalyses.map((analysis) => (
              <div key={analysis.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">{analysis.aircraft}</p>
                    <p className="text-sm text-gray-500">{analysis.date}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {analysis.issues > 0 && (
                    <span className="status-badge status-warning">
                      {analysis.issues} issues
                    </span>
                  )}
                  <span className={`status-badge ${
                    analysis.status === 'completed' ? 'status-success' : 'status-info'
                  }`}>
                    {analysis.status === 'completed' ? (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    ) : (
                      <Clock className="h-3 w-3 mr-1" />
                    )}
                    {analysis.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <button className="btn-secondary w-full">View All Analyses</button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full btn-primary text-left flex items-center">
              <FileText className="h-5 w-5 mr-3" />
              Start New MEL Analysis
            </button>
            <button className="w-full btn-secondary text-left flex items-center">
              <BarChart3 className="h-5 w-5 mr-3" />
              Generate Compliance Report
            </button>
            <button className="w-full btn-secondary text-left flex items-center">
              <AlertTriangle className="h-5 w-5 mr-3" />
              Review Flagged Items
            </button>
            <button className="w-full btn-secondary text-left flex items-center">
              <Users className="h-5 w-5 mr-3" />
              Manage Inspector Access
            </button>
          </div>
        </div>
      </div>

      {/* Compliance Overview */}
      <div className="mt-8 card">
        <h3 className="text-lg font-semibold mb-4">Compliance Overview</h3>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">94.2%</div>
            <div className="text-sm text-gray-600">Overall Compliance</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-600 mb-2">12</div>
            <div className="text-sm text-gray-600">Items Under Review</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-600 mb-2">3</div>
            <div className="text-sm text-gray-600">Critical Issues</div>
          </div>
        </div>
      </div>
    </div>
  )
}
