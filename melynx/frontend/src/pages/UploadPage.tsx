import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, FileText, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';

interface UploadResponse {
  success: boolean;
  evaluationId?: string;
  message?: string;
  error?: string;
  pageCount?: number;
  processedPages?: number;
}

const UploadPage: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [pageLimit, setPageLimit] = useState(50);
  const [enablePageLimit, setEnablePageLimit] = useState(true);
  const [dragActive, setDragActive] = useState(false);
  const navigate = useNavigate();

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    if (file.type !== 'application/pdf' && !file.name.toLowerCase().endsWith('.pdf')) {
      toast.error('Please upload a PDF file');
      return;
    }

    // Validate file size (50MB limit)
    if (file.size > 50 * 1024 * 1024) {
      toast.error('File size must be less than 50MB');
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      // Add page limiting options
      if (enablePageLimit) {
        formData.append('pageLimit', pageLimit.toString());
      }

      const response = await fetch('http://localhost:3001/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result: UploadResponse = await response.json();

      if (result.success && result.evaluationId) {
        toast.success(
          result.pageCount && result.processedPages
            ? `Document uploaded successfully! Processed ${result.processedPages} of ${result.pageCount} pages.`
            : 'Document uploaded successfully!'
        );
        navigate(`/evaluation/${result.evaluationId}`);
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Upload MEL Document
        </h1>
        <p className="text-lg text-gray-600">
          Upload your Minimum Equipment List (MEL) document for AI-powered compliance analysis
        </p>
      </div>

      {/* Page Limiting Options */}
      <div className="card mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Processing Options</h2>

        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="enablePageLimit"
              checked={enablePageLimit}
              onChange={(e) => setEnablePageLimit(e.target.checked)}
              className="h-4 w-4 text-aviation-blue focus:ring-aviation-blue border-gray-300 rounded"
            />
            <label htmlFor="enablePageLimit" className="ml-2 text-sm text-gray-700">
              Enable page limiting for large documents
            </label>
          </div>

          {enablePageLimit && (
            <div className="ml-6">
              <label htmlFor="pageLimit" className="block text-sm font-medium text-gray-700 mb-2">
                Maximum pages to process: {pageLimit}
              </label>
              <input
                type="range"
                id="pageLimit"
                min="10"
                max="200"
                step="10"
                value={pageLimit}
                onChange={(e) => setPageLimit(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>10 pages</span>
                <span>200 pages</span>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Large documents will be processed in chunks to ensure optimal performance and faster results.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Upload Area */}
      <div className="card">
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            dragActive
              ? 'border-aviation-blue bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
        >
          <input
            type="file"
            accept=".pdf"
            onChange={handleFileSelect}
            className="hidden"
            id="file-upload"
            disabled={isUploading}
          />

          {isUploading ? (
            <div className="space-y-4">
              <Loader2 className="h-12 w-12 text-aviation-blue mx-auto animate-spin" />
              <div>
                <p className="text-lg font-medium text-gray-900">Processing document...</p>
                <p className="text-sm text-gray-600">This may take a few moments</p>
              </div>
            </div>
          ) : (
            <label htmlFor="file-upload" className="cursor-pointer">
              <div className="space-y-4">
                <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    {dragActive ? 'Drop the PDF file here' : 'Drag & drop a PDF file here'}
                  </p>
                  <p className="text-sm text-gray-600">or click to select a file</p>
                </div>
                <div className="text-xs text-gray-500">
                  <p>Supported format: PDF</p>
                  <p>Maximum file size: 50MB</p>
                </div>
              </div>
            </label>
          )}
        </div>
      </div>

      {/* Information Cards */}
      <div className="grid md:grid-cols-2 gap-6 mt-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <CheckCircle className="h-6 w-6 text-blue-600 mt-1 mr-3" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">What we analyze</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• MEL compliance requirements</li>
                <li>• Safety critical items</li>
                <li>• Operational limitations</li>
                <li>• Maintenance procedures</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-amber-600 mt-1 mr-3" />
            <div>
              <h3 className="font-semibold text-amber-900 mb-2">Processing time</h3>
              <ul className="text-sm text-amber-800 space-y-1">
                <li>• Small documents (&lt;20 pages): ~30 seconds</li>
                <li>• Medium documents (20-50 pages): ~1-2 minutes</li>
                <li>• Large documents (&gt;50 pages): ~2-5 minutes</li>
                <li>• Page limiting reduces processing time</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UploadPage;