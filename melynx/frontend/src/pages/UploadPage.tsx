import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Upload, FileText, AlertCircle, CheckCircle, Loader2, Plane } from 'lucide-react'

export default function UploadPage() {
  const navigate = useNavigate()
  const [dragActive, setDragActive] = useState(false)
  const [files, setFiles] = useState<File[]>([])
  const [uploading, setUploading] = useState(false)
  const [uploadResults, setUploadResults] = useState<any[]>([])
  const [analyzing, setAnalyzing] = useState(false)
  const [formData, setFormData] = useState({
    aircraftType: '',
    operator: '',
    documentType: 'other',
    inspectorId: 'demo-inspector-123' // TODO: Get from auth context
  })

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const newFiles = Array.from(e.dataTransfer.files)
      setFiles(prev => [...prev, ...newFiles])
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files)
      setFiles(prev => [...prev, ...newFiles])
    }
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleUpload = async () => {
    if (files.length === 0) return

    setUploading(true)
    setUploadResults([])

    try {
      const formDataToSend = new FormData()

      // Add files
      files.forEach(file => {
        formDataToSend.append('files', file)
      })

      // Add metadata
      formDataToSend.append('aircraftType', formData.aircraftType)
      formDataToSend.append('operator', formData.operator)
      formDataToSend.append('documentType', formData.documentType)
      formDataToSend.append('inspectorId', formData.inspectorId)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formDataToSend
      })

      const result = await response.json()

      if (response.ok) {
        setUploadResults(result.results || [])
        setFiles([]) // Clear files after successful upload
      } else {
        throw new Error(result.message || 'Upload failed')
      }

    } catch (error) {
      console.error('Upload error:', error)
      alert(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setUploading(false)
    }
  }

  const handleStartAnalysis = async () => {
    if (uploadResults.length === 0) return

    setAnalyzing(true)

    try {
      // For now, we'll assume the first document is operator MEL and second is master MEL
      // In a real implementation, this would be determined by document type or user selection
      const operatorDoc = uploadResults.find(r => r.document.metadata.documentType === 'operator_mel') || uploadResults[0]
      const masterDoc = uploadResults.find(r => r.document.metadata.documentType === 'master_mel') || uploadResults[1] || uploadResults[0]

      if (!operatorDoc || !masterDoc) {
        throw new Error('Need at least one document to analyze. For comparison, upload both operator and master MEL documents.')
      }

      const response = await fetch('/api/analysis/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          operatorMelId: operatorDoc.document.id,
          masterMelId: masterDoc.document.id,
          createdBy: formData.inspectorId,
          options: {
            clauseByClauseComparison: true,
            complianceGapAnalysis: true,
            generateDetailedReport: true,
            enableRealtimeChat: false,
            includeRecommendations: true
          }
        })
      })

      const result = await response.json()

      if (response.ok) {
        // Show success message
        const confirmed = confirm(
          `Analysis started successfully!\n\n` +
          `Analysis ID: ${result.analysisId}\n` +
          `Estimated duration: ${result.estimatedDuration}\n\n` +
          `Would you like to go to the dashboard to monitor progress?`
        )

        if (confirmed) {
          navigate('/dashboard')
        }

        console.log('Analysis started:', result)
      } else {
        throw new Error(result.message || 'Failed to start analysis')
      }

    } catch (error) {
      console.error('Analysis start error:', error)
      alert(`Failed to start analysis: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setAnalyzing(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Upload MEL Documents
        </h1>
        <p className="text-lg text-gray-600">
          Upload your Operator MEL and Master MEL documents for AI-powered compliance analysis
        </p>
      </div>

      {/* Document Metadata Form */}
      <div className="card mb-8">
        <h3 className="text-lg font-semibold mb-4">Document Information</h3>
        <div className="grid md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Aircraft Type
            </label>
            <input
              type="text"
              className="input-field"
              placeholder="e.g., Boeing 737-800"
              value={formData.aircraftType}
              onChange={(e) => setFormData(prev => ({ ...prev, aircraftType: e.target.value }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Operator
            </label>
            <input
              type="text"
              className="input-field"
              placeholder="e.g., Sample Airlines"
              value={formData.operator}
              onChange={(e) => setFormData(prev => ({ ...prev, operator: e.target.value }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document Type
            </label>
            <select
              className="input-field"
              value={formData.documentType}
              onChange={(e) => setFormData(prev => ({ ...prev, documentType: e.target.value }))}
            >
              <option value="other">Auto-detect</option>
              <option value="operator_mel">Operator MEL</option>
              <option value="master_mel">Master MEL (MMEL)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Upload Area */}
      <div className="card mb-8">
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive 
              ? 'border-aviation-blue bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Drop your MEL files here
          </h3>
          <p className="text-gray-600 mb-4">
            or click to browse and select files
          </p>
          <input
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.txt,.xml"
            onChange={handleFileSelect}
            className="hidden"
            id="file-upload"
          />
          <label
            htmlFor="file-upload"
            className="btn-primary cursor-pointer inline-block"
          >
            Select Files
          </label>
          <p className="text-sm text-gray-500 mt-4">
            Supported formats: PDF, DOC, DOCX, TXT, XML (Max 50MB per file)
          </p>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="card mb-8">
          <h3 className="text-lg font-semibold mb-4">Uploaded Files</h3>
          <div className="space-y-3">
            {files.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">{file.name}</p>
                    <p className="text-sm text-gray-500">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="text-red-600 hover:text-red-800 text-sm font-medium"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Analysis Options */}
      <div className="card mb-8">
        <h3 className="text-lg font-semibold mb-4">Analysis Options</h3>
        <div className="space-y-4">
          <label className="flex items-center">
            <input type="checkbox" className="mr-3" defaultChecked />
            <span>Clause-by-clause comparison</span>
          </label>
          <label className="flex items-center">
            <input type="checkbox" className="mr-3" defaultChecked />
            <span>Compliance gap analysis</span>
          </label>
          <label className="flex items-center">
            <input type="checkbox" className="mr-3" defaultChecked />
            <span>Generate detailed report</span>
          </label>
          <label className="flex items-center">
            <input type="checkbox" className="mr-3" />
            <span>Enable real-time Q&A chat</span>
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <button
          className="btn-secondary"
          onClick={() => {
            setFiles([])
            setUploadResults([])
          }}
          disabled={uploading}
        >
          Clear All
        </button>
        <button
          className="btn-primary"
          disabled={files.length === 0 || uploading}
          onClick={handleUpload}
        >
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <CheckCircle className="mr-2 h-5 w-5" />
              Upload & Parse Documents
            </>
          )}
        </button>
      </div>

      {/* Upload Results */}
      {uploadResults.length > 0 && (
        <div className="mt-8 card">
          <h3 className="text-lg font-semibold mb-4">Upload Results</h3>
          <div className="space-y-4">
            {uploadResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">
                    {result.document.originalName}
                  </h4>
                  <span className={`status-badge ${
                    result.parsing.success ? 'status-success' : 'status-danger'
                  }`}>
                    {result.parsing.success ? (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    ) : (
                      <AlertCircle className="h-3 w-3 mr-1" />
                    )}
                    {result.parsing.success ? 'Parsed Successfully' : 'Parsing Failed'}
                  </span>
                </div>

                {result.parsing.success ? (
                  <div className="grid md:grid-cols-3 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Clauses Extracted:</span> {result.parsing.clausesExtracted}
                    </div>
                    <div>
                      <span className="font-medium">Document Type:</span> {result.document.metadata.documentType}
                    </div>
                    <div>
                      <span className="font-medium">Aircraft:</span> {result.document.metadata.aircraftType || 'Not specified'}
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-red-600">
                    <span className="font-medium">Error:</span> {result.parsing.error}
                  </div>
                )}

                {result.parsing.stats && (
                  <div className="mt-3 pt-3 border-t">
                    <div className="grid md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Total Clauses:</span>
                        <div className="text-lg font-bold text-aviation-blue">
                          {result.parsing.stats.totalClauses || result.parsing.clausesExtracted || 'N/A'}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Categories:</span>
                        <div className="text-sm text-gray-600">
                          {result.parsing.stats.categoryCounts ?
                            Object.entries(result.parsing.stats.categoryCounts).map(([cat, count]) => (
                              <span key={cat} className="mr-2">{cat}: {count}</span>
                            )) :
                            'N/A'
                          }
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">ATA Sections:</span>
                        <div className="text-sm text-gray-600">
                          {result.parsing.stats.sectionCounts ?
                            `${Object.keys(result.parsing.stats.sectionCounts).length} sections` :
                            'N/A'
                          }
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Status:</span>
                        <div className="text-sm font-medium text-green-600">
                          Ready for Analysis
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-6 text-center">
            <button
              className="btn-primary"
              onClick={handleStartAnalysis}
              disabled={uploadResults.length === 0 || analyzing}
            >
              {analyzing ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Starting Analysis...
                </>
              ) : (
                <>
                  <Plane className="mr-2 h-5 w-5" />
                  Start MEL Compliance Analysis
                </>
              )}
            </button>
          </div>
        </div>
      )}

      {/* Info Box */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900">Analysis Process</h4>
            <p className="text-sm text-blue-700 mt-1">
              Our AI will parse your documents, extract MEL clauses, compare them against master standards, 
              and generate a comprehensive compliance report. This typically takes 2-5 minutes depending on document size.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
