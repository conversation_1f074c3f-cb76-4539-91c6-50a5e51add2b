import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { 
  ArrowLeft, 
  Download, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  FileText,
  Plane,
  Calendar,
  User,
  Clock,
  BarChart3,
  MessageSquare
} from 'lucide-react'

interface EvaluationDetail {
  id: string
  status: string
  startedAt: string
  completedAt?: string
  operatorDocument: {
    originalName: string
    metadata: {
      aircraftType?: string
      operator?: string
      effectiveDate?: string
      revisionNumber?: string
    }
  }
  masterDocument: {
    originalName: string
    metadata: {
      effectiveDate?: string
      revisionNumber?: string
    }
  }
  results: {
    complianceRate: number
    totalClauses: number
    matchedClauses: number
    missingClauses: number
    modifiedClauses: number
    additionalClauses: number
    criticalIssues: any[]
    warnings: any[]
    recommendations: string[]
    summary: string
    clauseComparisons: any[]
  }
}

export default function EvaluationDetailPage() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [evaluation, setEvaluation] = useState<EvaluationDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('summary')
  const [approving, setApproving] = useState(false)
  const [rejecting, setRejecting] = useState(false)

  useEffect(() => {
    if (id) {
      fetchEvaluation(id)
    }
  }, [id])

  const fetchEvaluation = async (evaluationId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/analysis/results/${evaluationId}`)
      
      if (response.ok) {
        const data = await response.json()
        setEvaluation({
          id: evaluationId,
          status: data.status || 'completed',
          startedAt: new Date().toISOString(),
          completedAt: new Date().toISOString(),
          operatorDocument: {
            originalName: 'Operator MEL.pdf',
            metadata: {
              aircraftType: 'Boeing 737-800',
              operator: 'Sample Airlines'
            }
          },
          masterDocument: {
            originalName: 'Master MEL.pdf',
            metadata: {}
          },
          results: data.results
        })
      } else {
        console.error('Failed to fetch evaluation')
      }
    } catch (error) {
      console.error('Error fetching evaluation:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async () => {
    if (!id) return
    
    try {
      setApproving(true)
      const response = await fetch(`/api/reports/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inspectorId: 'demo-inspector-123',
          comments: 'Analysis approved after review'
        })
      })
      
      if (response.ok) {
        alert('Analysis approved successfully')
        navigate('/dashboard')
      } else {
        alert('Failed to approve analysis')
      }
    } catch (error) {
      console.error('Error approving analysis:', error)
      alert('Error approving analysis')
    } finally {
      setApproving(false)
    }
  }

  const handleReject = async () => {
    if (!id) return
    
    const comments = prompt('Please provide rejection comments:')
    if (!comments) return
    
    try {
      setRejecting(true)
      const response = await fetch(`/api/reports/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inspectorId: 'demo-inspector-123',
          comments,
          requiredChanges: ['Address critical issues', 'Review compliance gaps']
        })
      })
      
      if (response.ok) {
        alert('Analysis rejected - revision required')
        navigate('/dashboard')
      } else {
        alert('Failed to reject analysis')
      }
    } catch (error) {
      console.error('Error rejecting analysis:', error)
      alert('Error rejecting analysis')
    } finally {
      setRejecting(false)
    }
  }

  const downloadPDF = () => {
    if (id) {
      window.open(`/api/reports/${id}/pdf?inspectorName=Demo Inspector&inspectorLicense=12345`, '_blank')
    }
  }

  const downloadJSON = () => {
    if (id) {
      window.open(`/api/reports/${id}/json?inspectorName=Demo Inspector&inspectorLicense=12345`, '_blank')
    }
  }

  const getComplianceColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600'
    if (rate >= 85) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getRecommendation = (results: any) => {
    if (results.criticalIssues.length > 0) return { text: 'REJECT', color: 'bg-red-100 text-red-800' }
    if (results.complianceRate < 85) return { text: 'REJECT', color: 'bg-red-100 text-red-800' }
    if (results.complianceRate < 95) return { text: 'CONDITIONAL APPROVAL', color: 'bg-yellow-100 text-yellow-800' }
    return { text: 'APPROVE', color: 'bg-green-100 text-green-800' }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-aviation-blue"></div>
        </div>
      </div>
    )
  }

  if (!evaluation) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Evaluation Not Found</h2>
          <p className="text-gray-600 mb-4">The requested evaluation could not be found.</p>
          <button onClick={() => navigate('/dashboard')} className="btn-primary">
            Return to Dashboard
          </button>
        </div>
      </div>
    )
  }

  const recommendation = getRecommendation(evaluation.results)

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/dashboard')}
            className="btn-secondary mr-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              MEL Compliance Evaluation
            </h1>
            <p className="text-gray-600">Analysis ID: {evaluation.id}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button onClick={downloadJSON} className="btn-secondary">
            <Download className="h-4 w-4 mr-2" />
            JSON
          </button>
          <button onClick={downloadPDF} className="btn-secondary">
            <Download className="h-4 w-4 mr-2" />
            PDF Report
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6 mb-8">
        <div className="card text-center">
          <div className="flex items-center justify-center mb-2">
            <Plane className="h-6 w-6 text-aviation-blue" />
          </div>
          <div className="text-sm text-gray-600">Aircraft</div>
          <div className="font-semibold">{evaluation.operatorDocument.metadata.aircraftType}</div>
        </div>
        
        <div className="card text-center">
          <div className="flex items-center justify-center mb-2">
            <BarChart3 className="h-6 w-6 text-green-600" />
          </div>
          <div className="text-sm text-gray-600">Compliance Rate</div>
          <div className={`text-2xl font-bold ${getComplianceColor(evaluation.results.complianceRate)}`}>
            {evaluation.results.complianceRate}%
          </div>
        </div>
        
        <div className="card text-center">
          <div className="flex items-center justify-center mb-2">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <div className="text-sm text-gray-600">Critical Issues</div>
          <div className="text-2xl font-bold text-red-600">
            {evaluation.results.criticalIssues.length}
          </div>
        </div>
        
        <div className="card text-center">
          <div className="flex items-center justify-center mb-2">
            <CheckCircle className="h-6 w-6 text-blue-600" />
          </div>
          <div className="text-sm text-gray-600">Recommendation</div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${recommendation.color}`}>
            {recommendation.text}
          </div>
        </div>
      </div>

      {/* Document Information */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Operator MEL</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <FileText className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm">{evaluation.operatorDocument.originalName}</span>
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm">{evaluation.operatorDocument.metadata.operator}</span>
            </div>
            {evaluation.operatorDocument.metadata.effectiveDate && (
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-sm">Effective: {evaluation.operatorDocument.metadata.effectiveDate}</span>
              </div>
            )}
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Master MEL</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <FileText className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm">{evaluation.masterDocument.originalName}</span>
            </div>
            {evaluation.masterDocument.metadata.effectiveDate && (
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                <span className="text-sm">Effective: {evaluation.masterDocument.metadata.effectiveDate}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="card">
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'summary', label: 'Summary', icon: BarChart3 },
              { id: 'issues', label: 'Issues', icon: AlertTriangle },
              { id: 'clauses', label: 'Clause Details', icon: FileText },
              { id: 'recommendations', label: 'Recommendations', icon: MessageSquare }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-aviation-blue text-aviation-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'summary' && (
          <div className="space-y-6">
            <div>
              <h4 className="text-lg font-semibold mb-3">Analysis Summary</h4>
              <p className="text-gray-700 leading-relaxed">{evaluation.results.summary}</p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h5 className="font-semibold mb-3">Clause Statistics</h5>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Clauses:</span>
                    <span className="font-medium">{evaluation.results.totalClauses}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Matched:</span>
                    <span className="font-medium text-green-600">{evaluation.results.matchedClauses}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Missing:</span>
                    <span className="font-medium text-red-600">{evaluation.results.missingClauses}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Modified:</span>
                    <span className="font-medium text-yellow-600">{evaluation.results.modifiedClauses}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Additional:</span>
                    <span className="font-medium text-blue-600">{evaluation.results.additionalClauses}</span>
                  </div>
                </div>
              </div>

              <div>
                <h5 className="font-semibold mb-3">Issue Summary</h5>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Critical Issues:</span>
                    <span className="font-medium text-red-600">{evaluation.results.criticalIssues.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Warnings:</span>
                    <span className="font-medium text-yellow-600">{evaluation.results.warnings.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recommendations:</span>
                    <span className="font-medium">{evaluation.results.recommendations.length}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'issues' && (
          <div className="space-y-6">
            {evaluation.results.criticalIssues.length > 0 && (
              <div>
                <h4 className="text-lg font-semibold mb-3 text-red-600">Critical Issues</h4>
                <div className="space-y-4">
                  {evaluation.results.criticalIssues.map((issue, index) => (
                    <div key={index} className="border-l-4 border-red-500 pl-4 py-2">
                      <h5 className="font-semibold text-red-800">{issue.title}</h5>
                      <p className="text-gray-700 mt-1">{issue.description}</p>
                      <p className="text-sm text-gray-600 mt-2">
                        <strong>Recommendation:</strong> {issue.recommendation}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {evaluation.results.warnings.length > 0 && (
              <div>
                <h4 className="text-lg font-semibold mb-3 text-yellow-600">Warnings</h4>
                <div className="space-y-4">
                  {evaluation.results.warnings.map((warning, index) => (
                    <div key={index} className="border-l-4 border-yellow-500 pl-4 py-2">
                      <h5 className="font-semibold text-yellow-800">{warning.title}</h5>
                      <p className="text-gray-700 mt-1">{warning.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {evaluation.results.criticalIssues.length === 0 && evaluation.results.warnings.length === 0 && (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Issues Found</h3>
                <p className="text-gray-600">This analysis did not identify any critical issues or warnings.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'recommendations' && (
          <div>
            <h4 className="text-lg font-semibold mb-4">AI Recommendations</h4>
            {evaluation.results.recommendations.length > 0 ? (
              <div className="space-y-3">
                {evaluation.results.recommendations.map((rec, index) => (
                  <div key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-6 h-6 bg-aviation-blue text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                      {index + 1}
                    </span>
                    <p className="text-gray-700">{rec}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600">No specific recommendations available.</p>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4 mt-8">
        <button
          onClick={handleReject}
          disabled={rejecting}
          className="btn-danger"
        >
          {rejecting ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Rejecting...
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 mr-2" />
              Reject Analysis
            </>
          )}
        </button>

        <button
          onClick={handleApprove}
          disabled={approving}
          className="btn-success"
        >
          {approving ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Approving...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Approve Analysis
            </>
          )}
        </button>
      </div>
    </div>
  )
}
