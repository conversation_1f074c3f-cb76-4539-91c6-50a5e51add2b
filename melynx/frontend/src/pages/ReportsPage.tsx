import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Download, 
  Eye,
  Calendar,
  Clock,
  Search,
  Filter
} from 'lucide-react';
import toast from 'react-hot-toast';

interface EvaluationSummary {
  id: string;
  filename: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  compliance_score?: number;
  total_items?: number;
  critical_issues?: number;
  warnings?: number;
  page_count?: number;
  processed_pages?: number;
}

const ReportsPage: React.FC = () => {
  const [evaluations, setEvaluations] = useState<EvaluationSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    fetchEvaluations();
  }, []);

  const fetchEvaluations = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/evaluations');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch evaluations: ${response.statusText}`);
      }

      const data = await response.json();
      setEvaluations(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching evaluations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch evaluations');
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = async (evaluationId: string, filename: string) => {
    try {
      const response = await fetch(`http://localhost:3001/api/evaluations/${evaluationId}/report`);
      
      if (!response.ok) {
        throw new Error('Failed to download report');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `MEL_Report_${filename}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Report downloaded successfully');
    } catch (err) {
      console.error('Error downloading report:', err);
      toast.error('Failed to download report');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'processing':
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'processing':
        return 'Processing';
      case 'pending':
        return 'Pending';
      case 'failed':
        return 'Failed';
      default:
        return 'Unknown';
    }
  };

  const getComplianceScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredEvaluations = evaluations.filter(evaluation => {
    const matchesSearch = evaluation.filename.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || evaluation.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="text-center">
          <Clock className="h-12 w-12 text-aviation-blue mx-auto animate-pulse mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading reports...</h2>
          <p className="text-gray-600">Please wait while we fetch your evaluation reports.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Reports</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchEvaluations}
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Evaluation Reports</h1>
          <p className="text-gray-600">View and manage your MEL compliance evaluation reports</p>
        </div>
        <Link to="/upload" className="btn-primary">
          <FileText className="h-4 w-4 mr-2" />
          New Evaluation
        </Link>
      </div>

      {/* Filters */}
      <div className="card mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              <input
                type="text"
                placeholder="Search by filename..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <div className="relative">
              <Filter className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="input-field pl-10 appearance-none"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="processing">Processing</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Reports List */}
      {filteredEvaluations.length === 0 ? (
        <div className="card text-center">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {evaluations.length === 0 ? 'No Reports Yet' : 'No Reports Found'}
          </h3>
          <p className="text-gray-600 mb-4">
            {evaluations.length === 0 
              ? 'Upload your first MEL document to get started with compliance analysis.'
              : 'Try adjusting your search or filter criteria.'
            }
          </p>
          {evaluations.length === 0 && (
            <Link to="/upload" className="btn-primary">
              Upload MEL Document
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredEvaluations.map((evaluation) => (
            <div key={evaluation.id} className="card">
              <div className="flex items-center justify-between">
                <div className="flex items-center flex-1">
                  <FileText className="h-8 w-8 text-aviation-blue mr-4" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {evaluation.filename}
                    </h3>
                    <div className="flex items-center text-sm text-gray-600 space-x-4">
                      <div className="flex items-center">
                        {getStatusIcon(evaluation.status)}
                        <span className="ml-1">{getStatusText(evaluation.status)}</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{new Date(evaluation.created_at).toLocaleDateString()}</span>
                      </div>
                      {evaluation.page_count && (
                        <div>
                          <span>
                            {evaluation.processed_pages || evaluation.page_count} / {evaluation.page_count} pages
                          </span>
                          {evaluation.processed_pages && evaluation.processed_pages < evaluation.page_count && (
                            <span className="text-amber-600 ml-1">(limited)</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {evaluation.status === 'completed' && (
                    <>
                      {evaluation.compliance_score !== undefined && (
                        <div className="text-right">
                          <div className="text-sm text-gray-600">Compliance</div>
                          <div className={`text-lg font-semibold ${getComplianceScoreColor(evaluation.compliance_score)}`}>
                            {evaluation.compliance_score}%
                          </div>
                        </div>
                      )}
                      
                      {evaluation.critical_issues !== undefined && evaluation.critical_issues > 0 && (
                        <div className="text-right">
                          <div className="text-sm text-gray-600">Critical Issues</div>
                          <div className="text-lg font-semibold text-red-600">
                            {evaluation.critical_issues}
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  <div className="flex space-x-2">
                    <Link
                      to={`/evaluation/${evaluation.id}`}
                      className="btn-secondary"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Link>
                    
                    {evaluation.status === 'completed' && (
                      <button
                        onClick={() => downloadReport(evaluation.id, evaluation.filename)}
                        className="btn-primary"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReportsPage;
