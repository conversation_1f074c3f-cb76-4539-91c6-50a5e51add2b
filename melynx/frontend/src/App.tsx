import { Routes, Route } from 'react-router-dom'
import { Plane } from 'lucide-react'
import HomePage from './pages/HomePage'
import UploadPage from './pages/UploadPage'
import DashboardPage from './pages/DashboardPage'

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <nav className="bg-aviation-navy text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Plane className="h-8 w-8 mr-3" />
              <span className="text-xl font-bold">MELynx</span>
              <span className="ml-2 text-sm text-aviation-sky">
                AI-Powered MEL Compliance Inspector
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <a href="/" className="hover:text-aviation-sky transition-colors">
                Home
              </a>
              <a href="/upload" className="hover:text-aviation-sky transition-colors">
                Upload
              </a>
              <a href="/dashboard" className="hover:text-aviation-sky transition-colors">
                Dashboard
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/upload" element={<UploadPage />} />
          <Route path="/dashboard" element={<DashboardPage />} />
        </Routes>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p>&copy; 2024 MELynx. Aviation MEL Compliance Inspector.</p>
            <p className="text-sm text-gray-400 mt-2">
              Ensuring aviation safety through AI-powered compliance analysis.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
