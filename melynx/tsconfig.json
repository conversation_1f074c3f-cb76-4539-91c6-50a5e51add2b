{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@shared/*": ["./shared/src/*"], "@backend/*": ["./backend/src/*"], "@frontend/*": ["./frontend/src/*"]}}, "include": ["shared/src/**/*", "backend/src/**/*", "frontend/src/**/*"], "exclude": ["node_modules", "dist", "build", "coverage"], "references": [{"path": "./shared"}, {"path": "./backend"}, {"path": "./frontend"}]}