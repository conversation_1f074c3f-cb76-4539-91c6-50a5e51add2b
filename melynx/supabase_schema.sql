-- MELynx Supabase Database Schema
-- AI-Powered MEL Compliance Inspector
-- Created: 2024-01-15

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON><PERSON> custom types
CREATE TYPE document_status AS ENUM ('uploading', 'processing', 'completed', 'failed');
CREATE TYPE analysis_status AS ENUM ('initiated', 'processing', 'completed', 'failed', 'cancelled', 'approved', 'rejected');
CREATE TYPE issue_severity AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE issue_type AS ENUM ('missing_clause', 'modified_clause', 'additional_clause', 'conflicting_requirements', 'maintenance_interval', 'operational_procedure');
CREATE TYPE comparison_status AS ENUM ('matched', 'missing', 'modified', 'additional', 'conflicting');
CREATE TYPE mel_category AS ENUM ('A', 'B', 'C', 'D');

-- =====================================================
-- DOCUMENTS TABLE
-- Stores uploaded MEL documents (both operator and master)
-- =====================================================
CREATE TABLE mel_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    original_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    status document_status DEFAULT 'uploading',
    
    -- Document metadata
    metadata JSONB DEFAULT '{}',
    
    -- Processing results
    extracted_text TEXT,
    parsed_clauses JSONB DEFAULT '[]',
    parsing_errors JSONB DEFAULT '[]',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- User tracking
    uploaded_by VARCHAR(255),
    
    -- Indexes for performance
    CONSTRAINT mel_documents_file_size_check CHECK (file_size > 0)
);

-- =====================================================
-- MEL EVALUATIONS TABLE
-- Main analysis records linking operator and master MELs
-- =====================================================
CREATE TABLE mel_evaluations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Document references
    operator_mel_id UUID NOT NULL REFERENCES mel_documents(id) ON DELETE CASCADE,
    master_mel_id UUID NOT NULL REFERENCES mel_documents(id) ON DELETE CASCADE,
    
    -- Analysis configuration
    analysis_options JSONB DEFAULT '{}',
    
    -- Status and timing
    status analysis_status DEFAULT 'initiated',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    processing_time_ms INTEGER,
    
    -- Results summary
    compliance_rate DECIMAL(5,2),
    total_clauses INTEGER,
    matched_clauses INTEGER DEFAULT 0,
    missing_clauses INTEGER DEFAULT 0,
    modified_clauses INTEGER DEFAULT 0,
    additional_clauses INTEGER DEFAULT 0,
    conflicting_clauses INTEGER DEFAULT 0,
    
    -- AI Analysis results
    ai_summary TEXT,
    ai_recommendations JSONB DEFAULT '[]',
    
    -- Inspector review
    reviewed_by VARCHAR(255),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    inspector_comments TEXT,
    inspector_recommendation VARCHAR(50),
    
    -- Approval workflow
    approved_by VARCHAR(255),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_comments TEXT,
    approval_conditions JSONB DEFAULT '[]',
    
    rejected_by VARCHAR(255),
    rejected_at TIMESTAMP WITH TIME ZONE,
    rejection_comments TEXT,
    required_changes JSONB DEFAULT '[]',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255) NOT NULL,
    
    -- Constraints
    CONSTRAINT mel_evaluations_compliance_rate_check CHECK (compliance_rate >= 0 AND compliance_rate <= 100),
    CONSTRAINT mel_evaluations_clause_counts_check CHECK (
        total_clauses >= 0 AND 
        matched_clauses >= 0 AND 
        missing_clauses >= 0 AND 
        modified_clauses >= 0 AND 
        additional_clauses >= 0 AND
        conflicting_clauses >= 0
    )
);

-- =====================================================
-- CLAUSE COMPARISONS TABLE
-- Detailed clause-by-clause comparison results
-- =====================================================
CREATE TABLE clause_comparisons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    evaluation_id UUID NOT NULL REFERENCES mel_evaluations(id) ON DELETE CASCADE,
    
    -- Clause identification
    clause_number VARCHAR(50) NOT NULL,
    ata_chapter VARCHAR(10),
    section VARCHAR(50),
    
    -- Operator clause data
    operator_clause_id VARCHAR(100),
    operator_title TEXT,
    operator_content TEXT,
    operator_category mel_category,
    operator_conditions JSONB DEFAULT '[]',
    operator_limitations JSONB DEFAULT '[]',
    operator_maintenance_actions JSONB DEFAULT '[]',
    operator_placard TEXT,
    
    -- Master clause data
    master_clause_id VARCHAR(100),
    master_title TEXT,
    master_content TEXT,
    master_category mel_category,
    master_conditions JSONB DEFAULT '[]',
    master_limitations JSONB DEFAULT '[]',
    master_maintenance_actions JSONB DEFAULT '[]',
    master_placard TEXT,
    
    -- Comparison results
    status comparison_status NOT NULL,
    similarity_score DECIMAL(5,2),
    differences JSONB DEFAULT '[]',
    
    -- AI analysis
    ai_analysis TEXT,
    risk_assessment VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    CONSTRAINT clause_comparisons_similarity_check CHECK (similarity_score >= 0 AND similarity_score <= 100)
);

-- =====================================================
-- COMPLIANCE ISSUES TABLE
-- Individual compliance issues and findings
-- =====================================================
CREATE TABLE compliance_issues (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    evaluation_id UUID NOT NULL REFERENCES mel_evaluations(id) ON DELETE CASCADE,
    clause_comparison_id UUID REFERENCES clause_comparisons(id) ON DELETE CASCADE,
    
    -- Issue details
    issue_type issue_type NOT NULL,
    severity issue_severity NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    
    -- Clause context
    clause_number VARCHAR(50),
    ata_chapter VARCHAR(10),
    
    -- Recommendations
    recommendation TEXT,
    regulatory_reference VARCHAR(255),
    
    -- Resolution tracking
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by VARCHAR(255),
    resolution_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- AUDIT LOG TABLE
-- Complete audit trail for regulatory compliance
-- =====================================================
CREATE TABLE mel_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Context
    analysis_id UUID REFERENCES mel_evaluations(id) ON DELETE CASCADE,
    document_id UUID REFERENCES mel_documents(id) ON DELETE SET NULL,
    
    -- Action details
    action VARCHAR(100) NOT NULL,
    actor_id VARCHAR(255) NOT NULL,
    actor_type VARCHAR(50) DEFAULT 'user',
    
    -- Action data
    details JSONB DEFAULT '{}',
    old_values JSONB DEFAULT '{}',
    new_values JSONB DEFAULT '{}',
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Comments
    comments TEXT
);

-- =====================================================
-- ANALYSIS RESULTS TABLE
-- Structured storage of analysis results
-- =====================================================
CREATE TABLE analysis_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    evaluation_id UUID NOT NULL REFERENCES mel_evaluations(id) ON DELETE CASCADE,
    
    -- Results data
    results_data JSONB NOT NULL,
    
    -- Metadata
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    generated_by VARCHAR(255),
    version VARCHAR(20) DEFAULT '1.0.0',
    
    -- Performance metrics
    processing_time_ms INTEGER,
    memory_usage_mb DECIMAL(10,2),
    
    -- Validation
    is_valid BOOLEAN DEFAULT TRUE,
    validation_errors JSONB DEFAULT '[]',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- REPORTS TABLE
-- Generated report tracking and metadata
-- =====================================================
CREATE TABLE generated_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    evaluation_id UUID NOT NULL REFERENCES mel_evaluations(id) ON DELETE CASCADE,
    
    -- Report details
    report_type VARCHAR(20) NOT NULL CHECK (report_type IN ('pdf', 'json')),
    file_path TEXT,
    file_size BIGINT,
    
    -- Generation details
    generated_by VARCHAR(255) NOT NULL,
    generation_options JSONB DEFAULT '{}',
    
    -- Inspector details
    inspector_name VARCHAR(255),
    inspector_license VARCHAR(100),
    
    -- Status
    status VARCHAR(20) DEFAULT 'generated',
    download_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_downloaded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Documents table indexes
CREATE INDEX idx_mel_documents_status ON mel_documents(status);
CREATE INDEX idx_mel_documents_created_at ON mel_documents(created_at DESC);
CREATE INDEX idx_mel_documents_uploaded_by ON mel_documents(uploaded_by);
CREATE INDEX idx_mel_documents_metadata ON mel_documents USING GIN(metadata);

-- Evaluations table indexes
CREATE INDEX idx_mel_evaluations_status ON mel_evaluations(status);
CREATE INDEX idx_mel_evaluations_created_by ON mel_evaluations(created_by);
CREATE INDEX idx_mel_evaluations_created_at ON mel_evaluations(created_at DESC);
CREATE INDEX idx_mel_evaluations_operator_mel ON mel_evaluations(operator_mel_id);
CREATE INDEX idx_mel_evaluations_master_mel ON mel_evaluations(master_mel_id);
CREATE INDEX idx_mel_evaluations_compliance_rate ON mel_evaluations(compliance_rate DESC);

-- Clause comparisons indexes
CREATE INDEX idx_clause_comparisons_evaluation ON clause_comparisons(evaluation_id);
CREATE INDEX idx_clause_comparisons_clause_number ON clause_comparisons(clause_number);
CREATE INDEX idx_clause_comparisons_status ON clause_comparisons(status);
CREATE INDEX idx_clause_comparisons_ata_chapter ON clause_comparisons(ata_chapter);

-- Compliance issues indexes
CREATE INDEX idx_compliance_issues_evaluation ON compliance_issues(evaluation_id);
CREATE INDEX idx_compliance_issues_severity ON compliance_issues(severity);
CREATE INDEX idx_compliance_issues_type ON compliance_issues(issue_type);
CREATE INDEX idx_compliance_issues_resolved ON compliance_issues(resolved);
CREATE INDEX idx_compliance_issues_clause_number ON compliance_issues(clause_number);

-- Audit log indexes
CREATE INDEX idx_audit_log_analysis ON mel_audit_log(analysis_id);
CREATE INDEX idx_audit_log_actor ON mel_audit_log(actor_id);
CREATE INDEX idx_audit_log_action ON mel_audit_log(action);
CREATE INDEX idx_audit_log_created_at ON mel_audit_log(created_at DESC);

-- Analysis results indexes
CREATE INDEX idx_analysis_results_evaluation ON analysis_results(evaluation_id);
CREATE INDEX idx_analysis_results_generated_at ON analysis_results(generated_at DESC);

-- Reports indexes
CREATE INDEX idx_generated_reports_evaluation ON generated_reports(evaluation_id);
CREATE INDEX idx_generated_reports_type ON generated_reports(report_type);
CREATE INDEX idx_generated_reports_generated_by ON generated_reports(generated_by);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_mel_documents_updated_at
    BEFORE UPDATE ON mel_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mel_evaluations_updated_at
    BEFORE UPDATE ON mel_evaluations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_compliance_issues_updated_at
    BEFORE UPDATE ON compliance_issues
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE mel_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE mel_evaluations ENABLE ROW LEVEL SECURITY;
ALTER TABLE clause_comparisons ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_issues ENABLE ROW LEVEL SECURITY;
ALTER TABLE mel_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE generated_reports ENABLE ROW LEVEL SECURITY;

-- Basic policies (can be customized based on authentication system)
-- For now, allowing all operations for authenticated users

-- Documents policies
CREATE POLICY "Users can view all documents" ON mel_documents
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert documents" ON mel_documents
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update their documents" ON mel_documents
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Evaluations policies
CREATE POLICY "Users can view all evaluations" ON mel_evaluations
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert evaluations" ON mel_evaluations
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update evaluations" ON mel_evaluations
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Clause comparisons policies
CREATE POLICY "Users can view clause comparisons" ON clause_comparisons
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert clause comparisons" ON clause_comparisons
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Compliance issues policies
CREATE POLICY "Users can view compliance issues" ON compliance_issues
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert compliance issues" ON compliance_issues
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update compliance issues" ON compliance_issues
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Audit log policies (read-only for most users)
CREATE POLICY "Users can view audit logs" ON mel_audit_log
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "System can insert audit logs" ON mel_audit_log
    FOR INSERT WITH CHECK (true);

-- Analysis results policies
CREATE POLICY "Users can view analysis results" ON analysis_results
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert analysis results" ON analysis_results
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Reports policies
CREATE POLICY "Users can view reports" ON generated_reports
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert reports" ON generated_reports
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- =====================================================
-- STORAGE BUCKETS
-- =====================================================

-- Create storage buckets for file uploads
INSERT INTO storage.buckets (id, name, public) VALUES
    ('melynx-documents', 'melynx-documents', false),
    ('melynx-reports', 'melynx-reports', false);

-- Storage policies
CREATE POLICY "Authenticated users can upload documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'melynx-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Authenticated users can view documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'melynx-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Authenticated users can upload reports" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'melynx-reports' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Authenticated users can view reports" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'melynx-reports' AND
        auth.role() = 'authenticated'
    );

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample master MEL document
INSERT INTO mel_documents (
    id,
    original_name,
    file_path,
    file_size,
    mime_type,
    status,
    metadata,
    uploaded_by
) VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    'Boeing_737_MMEL_Rev15.pdf',
    'documents/master/boeing_737_mmel_rev15.pdf',
    2048576,
    'application/pdf',
    'completed',
    '{"aircraftType": "Boeing 737-800", "effectiveDate": "2024-01-15", "revisionNumber": "15", "authority": "FAA"}',
    'system'
);

-- Insert sample operator MEL document
INSERT INTO mel_documents (
    id,
    original_name,
    file_path,
    file_size,
    mime_type,
    status,
    metadata,
    uploaded_by
) VALUES (
    '550e8400-e29b-41d4-a716-446655440001',
    'Sample_Airlines_737_MEL_Rev12.pdf',
    'documents/operator/sample_airlines_737_mel_rev12.pdf',
    1536000,
    'application/pdf',
    'completed',
    '{"aircraftType": "Boeing 737-800", "operator": "Sample Airlines", "effectiveDate": "2024-01-01", "revisionNumber": "12"}',
    'demo-inspector-123'
);

-- =====================================================
-- FUNCTIONS FOR COMMON OPERATIONS
-- =====================================================

-- Function to get evaluation summary
CREATE OR REPLACE FUNCTION get_evaluation_summary(eval_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'id', e.id,
        'status', e.status,
        'compliance_rate', e.compliance_rate,
        'total_clauses', e.total_clauses,
        'critical_issues', (
            SELECT COUNT(*) FROM compliance_issues ci
            WHERE ci.evaluation_id = e.id AND ci.severity = 'critical'
        ),
        'warnings', (
            SELECT COUNT(*) FROM compliance_issues ci
            WHERE ci.evaluation_id = e.id AND ci.severity IN ('high', 'medium')
        ),
        'operator_document', (
            SELECT json_build_object(
                'name', od.original_name,
                'metadata', od.metadata
            ) FROM mel_documents od WHERE od.id = e.operator_mel_id
        ),
        'master_document', (
            SELECT json_build_object(
                'name', md.original_name,
                'metadata', md.metadata
            ) FROM mel_documents md WHERE md.id = e.master_mel_id
        )
    ) INTO result
    FROM mel_evaluations e
    WHERE e.id = eval_id;

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
    p_analysis_id UUID,
    p_action VARCHAR(100),
    p_actor_id VARCHAR(255),
    p_details JSONB DEFAULT '{}',
    p_comments TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO mel_audit_log (
        analysis_id,
        action,
        actor_id,
        details,
        comments
    ) VALUES (
        p_analysis_id,
        p_action,
        p_actor_id,
        p_details,
        p_comments
    ) RETURNING id INTO audit_id;

    RETURN audit_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for evaluation dashboard
CREATE VIEW evaluation_dashboard AS
SELECT
    e.id,
    e.status,
    e.compliance_rate,
    e.total_clauses,
    e.matched_clauses,
    e.missing_clauses,
    e.modified_clauses,
    e.additional_clauses,
    e.started_at,
    e.completed_at,
    e.created_by,
    od.original_name as operator_document_name,
    od.metadata->>'aircraftType' as aircraft_type,
    od.metadata->>'operator' as operator,
    md.original_name as master_document_name,
    (SELECT COUNT(*) FROM compliance_issues ci WHERE ci.evaluation_id = e.id AND ci.severity = 'critical') as critical_issues_count,
    (SELECT COUNT(*) FROM compliance_issues ci WHERE ci.evaluation_id = e.id AND ci.severity IN ('high', 'medium')) as warnings_count
FROM mel_evaluations e
JOIN mel_documents od ON e.operator_mel_id = od.id
JOIN mel_documents md ON e.master_mel_id = md.id
ORDER BY e.created_at DESC;

-- View for compliance issues summary
CREATE VIEW compliance_issues_summary AS
SELECT
    evaluation_id,
    COUNT(*) as total_issues,
    COUNT(*) FILTER (WHERE severity = 'critical') as critical_count,
    COUNT(*) FILTER (WHERE severity = 'high') as high_count,
    COUNT(*) FILTER (WHERE severity = 'medium') as medium_count,
    COUNT(*) FILTER (WHERE severity = 'low') as low_count,
    COUNT(*) FILTER (WHERE resolved = true) as resolved_count
FROM compliance_issues
GROUP BY evaluation_id;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

-- Add a comment to indicate schema completion
COMMENT ON SCHEMA public IS 'MELynx AI-Powered MEL Compliance Inspector Database Schema - Version 1.0.0';

-- Log schema creation
DO $$
BEGIN
    RAISE NOTICE 'MELynx Supabase schema created successfully!';
    RAISE NOTICE 'Tables created: %, %, %, %, %, %, %',
        'mel_documents', 'mel_evaluations', 'clause_comparisons',
        'compliance_issues', 'mel_audit_log', 'analysis_results', 'generated_reports';
    RAISE NOTICE 'Storage buckets: melynx-documents, melynx-reports';
    RAISE NOTICE 'Ready for MELynx application deployment!';
END $$;
