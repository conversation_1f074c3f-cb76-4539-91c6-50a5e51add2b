export declare const FILE_UPLOAD: {
    readonly MAX_SIZE: number;
    readonly MAX_FILES: 10;
    readonly ALLOWED_TYPES: readonly ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain", "application/xml", "text/xml"];
    readonly ALLOWED_EXTENSIONS: readonly [".pdf", ".doc", ".docx", ".txt", ".xml"];
};
export declare const ANALYSIS: {
    readonly MAX_PROCESSING_TIME: number;
    readonly ESTIMATED_TIME: {
        readonly SMALL_DOCUMENT: number;
        readonly MEDIUM_DOCUMENT: number;
        readonly LARGE_DOCUMENT: number;
    };
    readonly COMPLIANCE_THRESHOLDS: {
        readonly EXCELLENT: 98;
        readonly GOOD: 95;
        readonly ACCEPTABLE: 90;
        readonly POOR: 80;
    };
};
export declare const MEL_STANDARDS: {
    readonly AIRCRAFT_TYPES: readonly ["Boeing 737", "Boeing 747", "Boeing 757", "Boeing 767", "Boeing 777", "Boeing 787", "Airbus A320", "Airbus A330", "Airbus A340", "Airbus A350", "Airbus A380", "Embraer E-Jets", "Bombardier CRJ", "ATR 42/72"];
    readonly CATEGORIES: readonly ["Powerplant", "Landing Gear", "Flight Controls", "Navigation", "Communication", "Electrical", "Hydraulic", "Pneumatic", "Environmental", "Emergency Equipment", "Instruments", "Autopilot", "Ice and Rain Protection", "Fuel System", "APU"];
    readonly SEVERITY_LEVELS: {
        readonly CRITICAL: {
            readonly label: "Critical";
            readonly color: "#dc3545";
            readonly description: "Safety-critical issues requiring immediate attention";
        };
        readonly HIGH: {
            readonly label: "High";
            readonly color: "#fd7e14";
            readonly description: "Significant compliance issues";
        };
        readonly MEDIUM: {
            readonly label: "Medium";
            readonly color: "#ffc107";
            readonly description: "Moderate compliance concerns";
        };
        readonly LOW: {
            readonly label: "Low";
            readonly color: "#20c997";
            readonly description: "Minor issues or recommendations";
        };
        readonly INFO: {
            readonly label: "Info";
            readonly color: "#6f42c1";
            readonly description: "Informational notes";
        };
    };
};
export declare const API: {
    readonly RATE_LIMITS: {
        readonly GENERAL: 100;
        readonly UPLOAD: 10;
        readonly ANALYSIS: 5;
    };
    readonly TIMEOUTS: {
        readonly DEFAULT: 30000;
        readonly UPLOAD: 300000;
        readonly ANALYSIS: 1800000;
    };
    readonly PAGINATION: {
        readonly DEFAULT_LIMIT: 20;
        readonly MAX_LIMIT: 100;
    };
};
export declare const CHAT: {
    readonly MAX_MESSAGE_LENGTH: 2000;
    readonly MAX_HISTORY_LENGTH: 100;
    readonly SESSION_TIMEOUT: number;
    readonly CONTEXTS: {
        readonly GENERAL: "general";
        readonly ANALYSIS_SPECIFIC: "analysis_specific";
        readonly CLAUSE_SPECIFIC: "clause_specific";
    };
    readonly AI_MODELS: {
        readonly PRIMARY: "meta-llama/llama-3.3-70b-instruct";
        readonly FALLBACK: "meta-llama/llama-3.1-70b-instruct";
    };
};
export declare const REGULATIONS: {
    readonly FAA: {
        readonly name: "Federal Aviation Administration";
        readonly regulations: readonly ["14 CFR Part 91", "14 CFR Part 121", "14 CFR Part 135"];
    };
    readonly EASA: {
        readonly name: "European Union Aviation Safety Agency";
        readonly regulations: readonly ["EU-OPS", "Part-CAT", "Part-SPA"];
    };
    readonly ICAO: {
        readonly name: "International Civil Aviation Organization";
        readonly regulations: readonly ["Annex 6", "Doc 8335"];
    };
    readonly TCCA: {
        readonly name: "Transport Canada Civil Aviation";
        readonly regulations: readonly ["CAR 705", "CAR 704", "CAR 703"];
    };
};
export declare const UI: {
    readonly COLORS: {
        readonly AVIATION_BLUE: "#0066cc";
        readonly AVIATION_NAVY: "#003366";
        readonly AVIATION_SKY: "#87ceeb";
        readonly SUCCESS: "#28a745";
        readonly WARNING: "#ffc107";
        readonly DANGER: "#dc3545";
        readonly INFO: "#17a2b8";
    };
    readonly BREAKPOINTS: {
        readonly SM: "640px";
        readonly MD: "768px";
        readonly LG: "1024px";
        readonly XL: "1280px";
    };
    readonly ANIMATIONS: {
        readonly FAST: "150ms";
        readonly NORMAL: "300ms";
        readonly SLOW: "500ms";
    };
};
export declare const ERROR_MESSAGES: {
    readonly FILE_TOO_LARGE: "File size exceeds the maximum limit of 50MB";
    readonly INVALID_FILE_TYPE: "Invalid file type. Only PDF, DOC, DOCX, TXT, and XML files are allowed";
    readonly UPLOAD_FAILED: "File upload failed. Please try again";
    readonly ANALYSIS_FAILED: "Analysis failed to complete. Please check your documents and try again";
    readonly NETWORK_ERROR: "Network error. Please check your connection and try again";
    readonly UNAUTHORIZED: "You are not authorized to perform this action";
    readonly SESSION_EXPIRED: "Your session has expired. Please log in again";
};
//# sourceMappingURL=index.d.ts.map