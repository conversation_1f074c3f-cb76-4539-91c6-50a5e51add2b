// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_FILES: 10,
  ALLOWED_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'application/xml',
    'text/xml'
  ],
  ALLOWED_EXTENSIONS: ['.pdf', '.doc', '.docx', '.txt', '.xml']
} as const

// Analysis Constants
export const ANALYSIS = {
  MAX_PROCESSING_TIME: 30 * 60 * 1000, // 30 minutes
  ESTIMATED_TIME: {
    SMALL_DOCUMENT: 2 * 60 * 1000, // 2 minutes
    MEDIUM_DOCUMENT: 5 * 60 * 1000, // 5 minutes
    LARGE_DOCUMENT: 10 * 60 * 1000 // 10 minutes
  },
  COMPLIANCE_THRESHOLDS: {
    EXCELLENT: 98,
    GOOD: 95,
    ACCEPTABLE: 90,
    POOR: 80
  }
} as const

// MEL Standards
export const MEL_STANDARDS = {
  AIRCRAFT_TYPES: [
    'Boeing 737',
    'Boeing 747',
    'Boeing 757',
    'Boeing 767',
    'Boeing 777',
    'Boeing 787',
    'Airbus A320',
    'Airbus A330',
    'Airbus A340',
    'Airbus A350',
    'Airbus A380',
    'Embraer E-Jets',
    'Bombardier CRJ',
    'ATR 42/72'
  ],
  
  CATEGORIES: [
    'Powerplant',
    'Landing Gear',
    'Flight Controls',
    'Navigation',
    'Communication',
    'Electrical',
    'Hydraulic',
    'Pneumatic',
    'Environmental',
    'Emergency Equipment',
    'Instruments',
    'Autopilot',
    'Ice and Rain Protection',
    'Fuel System',
    'APU'
  ],
  
  SEVERITY_LEVELS: {
    CRITICAL: {
      label: 'Critical',
      color: '#dc3545',
      description: 'Safety-critical issues requiring immediate attention'
    },
    HIGH: {
      label: 'High',
      color: '#fd7e14',
      description: 'Significant compliance issues'
    },
    MEDIUM: {
      label: 'Medium',
      color: '#ffc107',
      description: 'Moderate compliance concerns'
    },
    LOW: {
      label: 'Low',
      color: '#20c997',
      description: 'Minor issues or recommendations'
    },
    INFO: {
      label: 'Info',
      color: '#6f42c1',
      description: 'Informational notes'
    }
  }
} as const

// API Constants
export const API = {
  RATE_LIMITS: {
    GENERAL: 100, // requests per 15 minutes
    UPLOAD: 10,   // uploads per 15 minutes
    ANALYSIS: 5   // analyses per hour
  },
  
  TIMEOUTS: {
    DEFAULT: 30000,     // 30 seconds
    UPLOAD: 300000,     // 5 minutes
    ANALYSIS: 1800000   // 30 minutes
  },
  
  PAGINATION: {
    DEFAULT_LIMIT: 20,
    MAX_LIMIT: 100
  }
} as const

// Chat Constants
export const CHAT = {
  MAX_MESSAGE_LENGTH: 2000,
  MAX_HISTORY_LENGTH: 100,
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  
  CONTEXTS: {
    GENERAL: 'general',
    ANALYSIS_SPECIFIC: 'analysis_specific',
    CLAUSE_SPECIFIC: 'clause_specific'
  },
  
  AI_MODELS: {
    PRIMARY: 'meta-llama/llama-3.3-70b-instruct',
    FALLBACK: 'meta-llama/llama-3.1-70b-instruct'
  }
} as const

// Regulatory References
export const REGULATIONS = {
  FAA: {
    name: 'Federal Aviation Administration',
    regulations: ['14 CFR Part 91', '14 CFR Part 121', '14 CFR Part 135']
  },
  EASA: {
    name: 'European Union Aviation Safety Agency',
    regulations: ['EU-OPS', 'Part-CAT', 'Part-SPA']
  },
  ICAO: {
    name: 'International Civil Aviation Organization',
    regulations: ['Annex 6', 'Doc 8335']
  },
  TCCA: {
    name: 'Transport Canada Civil Aviation',
    regulations: ['CAR 705', 'CAR 704', 'CAR 703']
  }
} as const

// UI Constants
export const UI = {
  COLORS: {
    AVIATION_BLUE: '#0066cc',
    AVIATION_NAVY: '#003366',
    AVIATION_SKY: '#87ceeb',
    SUCCESS: '#28a745',
    WARNING: '#ffc107',
    DANGER: '#dc3545',
    INFO: '#17a2b8'
  },
  
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px'
  },
  
  ANIMATIONS: {
    FAST: '150ms',
    NORMAL: '300ms',
    SLOW: '500ms'
  }
} as const

// Error Messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
  INVALID_FILE_TYPE: 'Invalid file type. Only PDF, DOC, DOCX, TXT, and XML files are allowed',
  UPLOAD_FAILED: 'File upload failed. Please try again',
  ANALYSIS_FAILED: 'Analysis failed to complete. Please check your documents and try again',
  NETWORK_ERROR: 'Network error. Please check your connection and try again',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  SESSION_EXPIRED: 'Your session has expired. Please log in again'
} as const
