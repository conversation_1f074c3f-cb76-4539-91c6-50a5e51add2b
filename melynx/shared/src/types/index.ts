// MEL Document Types
export interface MELDocument {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  uploadedAt: string
  parsedAt?: string
  status: DocumentStatus
  metadata: DocumentMetadata
  clauses: MELClause[]
}

export interface DocumentMetadata {
  aircraftType?: string
  operator?: string
  effectiveDate?: string
  revisionNumber?: string
  documentType: 'operator_mel' | 'master_mel' | 'other'
  pageCount?: number
  language?: string
}

export interface MELClause {
  id: string
  clauseNumber: string
  title: string
  content: string
  category: string
  subcategory?: string
  conditions?: string[]
  limitations?: string[]
  maintenanceActions?: string[]
  operationalProcedures?: string[]
  placard?: string
  remarks?: string
  page?: number
  section?: string
}

// Analysis Types
export interface MELAnalysis {
  id: string
  operatorMelId: string
  masterMelId: string
  status: AnalysisStatus
  startedAt: string
  completedAt?: string
  results?: AnalysisResults
  options: AnalysisOptions
  createdBy: string
}

export interface AnalysisOptions {
  clauseByClauseComparison: boolean
  complianceGapAnalysis: boolean
  generateDetailedReport: boolean
  enableRealtimeChat: boolean
  includeRecommendations: boolean
}

export interface AnalysisResults {
  complianceRate: number
  totalClauses: number
  matchedClauses: number
  missingClauses: number
  modifiedClauses: number
  additionalClauses: number
  criticalIssues: ComplianceIssue[]
  warnings: ComplianceIssue[]
  recommendations: string[]
  summary: string
  clauseComparisons: ClauseComparison[]
}

export interface ClauseComparison {
  operatorClause: MELClause
  masterClause?: MELClause
  status: ComparisonStatus
  differences: string[]
  severity: IssueSeverity
  recommendation?: string
}

export interface ComplianceIssue {
  id: string
  type: IssueType
  severity: IssueSeverity
  clauseId: string
  title: string
  description: string
  recommendation: string
  regulatoryReference?: string
}

// Chat Types
export interface ChatSession {
  id: string
  analysisId?: string
  createdAt: string
  lastMessageAt: string
  status: ChatStatus
  context: ChatContext
  messages: ChatMessage[]
}

export interface ChatMessage {
  id: string
  sessionId: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  metadata?: {
    sources?: string[]
    confidence?: number
    processingTime?: number
  }
}

export interface ChatContext {
  type: 'general' | 'analysis_specific' | 'clause_specific'
  analysisId?: string
  clauseId?: string
  aircraftType?: string
  regulations?: string[]
}

// User and Authentication Types
export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  organization?: string
  permissions: Permission[]
  createdAt: string
  lastLoginAt?: string
}

export interface Inspector extends User {
  licenseNumber?: string
  certifications: string[]
  specializations: string[]
  approvedAircraftTypes: string[]
}

// Enums
export enum DocumentStatus {
  UPLOADED = 'uploaded',
  PARSING = 'parsing',
  PARSED = 'parsed',
  FAILED = 'failed',
  ARCHIVED = 'archived'
}

export enum AnalysisStatus {
  INITIATED = 'initiated',
  PARSING_DOCUMENTS = 'parsing_documents',
  EXTRACTING_CLAUSES = 'extracting_clauses',
  COMPARING_CLAUSES = 'comparing_clauses',
  GENERATING_SUMMARY = 'generating_summary',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum ComparisonStatus {
  MATCHED = 'matched',
  MISSING = 'missing',
  MODIFIED = 'modified',
  ADDITIONAL = 'additional',
  CONFLICTING = 'conflicting'
}

export enum IssueSeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  INFO = 'info'
}

export enum IssueType {
  MISSING_CLAUSE = 'missing_clause',
  MODIFIED_CLAUSE = 'modified_clause',
  ADDITIONAL_CLAUSE = 'additional_clause',
  CONFLICTING_REQUIREMENTS = 'conflicting_requirements',
  FORMATTING_ISSUE = 'formatting_issue',
  REGULATORY_COMPLIANCE = 'regulatory_compliance'
}

export enum ChatStatus {
  ACTIVE = 'active',
  ENDED = 'ended',
  ARCHIVED = 'archived'
}

export enum UserRole {
  ADMIN = 'admin',
  INSPECTOR = 'inspector',
  OPERATOR = 'operator',
  VIEWER = 'viewer'
}

export enum Permission {
  UPLOAD_DOCUMENTS = 'upload_documents',
  RUN_ANALYSIS = 'run_analysis',
  VIEW_RESULTS = 'view_results',
  GENERATE_REPORTS = 'generate_reports',
  MANAGE_USERS = 'manage_users',
  ACCESS_CHAT = 'access_chat',
  EXPORT_DATA = 'export_data'
}
