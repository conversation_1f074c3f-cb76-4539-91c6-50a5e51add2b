// Enums
export var DocumentStatus;
(function (DocumentStatus) {
    DocumentStatus["UPLOADED"] = "uploaded";
    DocumentStatus["PARSING"] = "parsing";
    DocumentStatus["PARSED"] = "parsed";
    DocumentStatus["FAILED"] = "failed";
    DocumentStatus["ARCHIVED"] = "archived";
})(DocumentStatus || (DocumentStatus = {}));
export var AnalysisStatus;
(function (AnalysisStatus) {
    AnalysisStatus["INITIATED"] = "initiated";
    AnalysisStatus["PARSING_DOCUMENTS"] = "parsing_documents";
    AnalysisStatus["EXTRACTING_CLAUSES"] = "extracting_clauses";
    AnalysisStatus["COMPARING_CLAUSES"] = "comparing_clauses";
    AnalysisStatus["GENERATING_SUMMARY"] = "generating_summary";
    AnalysisStatus["COMPLETED"] = "completed";
    AnalysisStatus["FAILED"] = "failed";
    AnalysisStatus["CANCELLED"] = "cancelled";
})(AnalysisStatus || (AnalysisStatus = {}));
export var ComparisonStatus;
(function (ComparisonStatus) {
    ComparisonStatus["MATCHED"] = "matched";
    ComparisonStatus["MISSING"] = "missing";
    ComparisonStatus["MODIFIED"] = "modified";
    ComparisonStatus["ADDITIONAL"] = "additional";
    ComparisonStatus["CONFLICTING"] = "conflicting";
})(ComparisonStatus || (ComparisonStatus = {}));
export var IssueSeverity;
(function (IssueSeverity) {
    IssueSeverity["CRITICAL"] = "critical";
    IssueSeverity["HIGH"] = "high";
    IssueSeverity["MEDIUM"] = "medium";
    IssueSeverity["LOW"] = "low";
    IssueSeverity["INFO"] = "info";
})(IssueSeverity || (IssueSeverity = {}));
export var IssueType;
(function (IssueType) {
    IssueType["MISSING_CLAUSE"] = "missing_clause";
    IssueType["MODIFIED_CLAUSE"] = "modified_clause";
    IssueType["ADDITIONAL_CLAUSE"] = "additional_clause";
    IssueType["CONFLICTING_REQUIREMENTS"] = "conflicting_requirements";
    IssueType["FORMATTING_ISSUE"] = "formatting_issue";
    IssueType["REGULATORY_COMPLIANCE"] = "regulatory_compliance";
})(IssueType || (IssueType = {}));
export var ChatStatus;
(function (ChatStatus) {
    ChatStatus["ACTIVE"] = "active";
    ChatStatus["ENDED"] = "ended";
    ChatStatus["ARCHIVED"] = "archived";
})(ChatStatus || (ChatStatus = {}));
export var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["INSPECTOR"] = "inspector";
    UserRole["OPERATOR"] = "operator";
    UserRole["VIEWER"] = "viewer";
})(UserRole || (UserRole = {}));
export var Permission;
(function (Permission) {
    Permission["UPLOAD_DOCUMENTS"] = "upload_documents";
    Permission["RUN_ANALYSIS"] = "run_analysis";
    Permission["VIEW_RESULTS"] = "view_results";
    Permission["GENERATE_REPORTS"] = "generate_reports";
    Permission["MANAGE_USERS"] = "manage_users";
    Permission["ACCESS_CHAT"] = "access_chat";
    Permission["EXPORT_DATA"] = "export_data";
})(Permission || (Permission = {}));
//# sourceMappingURL=index.js.map