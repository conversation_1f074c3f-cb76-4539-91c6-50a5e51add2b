# MELynx — AI-Powered MEL Compliance Inspector ✈️

**Context Snapshot:**
- **Project:** MELynx - AI-Powered MEL Compliance Inspector
- **Mission:** Production-grade aviation MEL compliance automation
- **Core Function:** Compare Operator MELs vs Master MELs (MMELs), detect compliance issues, provide clause-level analysis
- **Primary Users:** Aviation inspectors, regulators, AMOs, air operators
- **Tech Stack:** Node.js/TypeScript + React/Tailwind + Supabase + OpenRouter (Meta LLaMA 3.3) + RAG

## 🎯 Project Overview

MELynx is a modular, production-grade application for aviation MEL (Minimum Equipment List) compliance. It compares Operator MELs with their corresponding Master MELs (MMELs), detects compliance issues, and provides clause-level analysis, reporting, and real-time Q&A for inspectors.

## 🏗️ Architecture

```
melynx/
├── frontend/           # React + Tailwind CSS app
│   ├── pages/         # Application pages
│   ├── components/    # Reusable UI components
│   ├── styles/        # Global styles and themes
│   └── hooks/         # Custom React hooks
├── backend/           # Node.js/TypeScript API and services
│   ├── services/      # Business logic modules
│   ├── routes/        # API endpoints
│   ├── supabase/      # Database schema and migrations
│   ├── utils/         # Shared utilities
│   └── tests/         # Backend tests
├── shared/            # Shared type definitions and constants
├── .env.template      # Environment variables template
└── package.json       # Monorepo configuration
```

## 🚀 Core Workflows

1. **MEL File Upload & Parsing** - Handle various MEL document formats
2. **Clause-by-Clause Comparison** - Compare Operator MEL vs MMEL
3. **AI-Powered Analysis** - Detect compliance issues using LLaMA 3.3
4. **Compliance Reporting** - Generate detailed audit reports
5. **Real-time Q&A** - RAG-powered inspector assistance

## 🛠️ Setup Instructions

### Prerequisites
- Node.js 18+ and npm/yarn
- Supabase account and project
- OpenRouter API key

### Environment Setup
1. Copy `.env.template` to `.env`
2. Fill in your Supabase and OpenRouter credentials
3. Run database migrations

### Development
```bash
# Install dependencies
npm install

# Start frontend development server
cd frontend && npm run dev

# Start backend API server
cd backend && npm run dev

# Run tests
npm test
```

## 🧪 Testing Strategy

- **Unit Tests:** Individual service modules
- **Integration Tests:** API endpoints and database operations
- **E2E Tests:** Complete MEL comparison workflows
- **Compliance Tests:** Validate against aviation standards

## 📋 Development Status

- [x] Project scaffolding and structure
- [ ] File upload and parsing modules
- [ ] MEL comparison engine
- [ ] AI summarization service
- [ ] Reporting system
- [ ] Q&A chat interface
- [ ] Production deployment

## 🔒 Security & Compliance

- Aviation-grade data security
- Audit trail for all operations
- Role-based access control
- Compliance with aviation regulations

## 📞 Support

For technical questions or aviation compliance guidance, refer to the documentation in each module or contact the development team.

---

**Next Steps:** Implement Upload and Parsing Modules (Power Prompt 3)
