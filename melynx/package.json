{"name": "melynx", "version": "1.0.0", "description": "AI-Powered MEL Compliance Inspector for Aviation", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules", "setup": "npm install && npm run build:shared"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["aviation", "mel", "compliance", "ai", "inspector", "typescript", "react", "supabase"], "author": "MELynx Development Team", "license": "MIT"}