{"name": "melynx", "version": "1.0.0", "description": "AI-powered MEL compliance inspector for aviation", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules shared/node_modules", "setup": "npm install && npm run build:shared"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["aviation", "mel", "compliance", "ai", "inspector", "typescript", "react", "supabase"], "author": "MELynx Development Team", "license": "MIT"}