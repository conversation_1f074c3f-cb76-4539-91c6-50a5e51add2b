<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MELynx - OCR Page Limiting Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .processing-note {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
        }
        .success-card {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-left: 4px solid #10b981;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">MELynx OCR Demo</h1>
                <p class="text-lg text-gray-600">AI-Powered MEL Compliance Inspector with Smart Page Limiting</p>
            </div>

            <!-- Upload Form -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4">Upload MEL Documents</h2>
                <form id="uploadForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Files</label>
                        <input type="file" id="fileInput" multiple accept=".pdf,.txt,.doc,.docx" 
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Aircraft Type</label>
                            <input type="text" id="aircraftType" value="Boeing 737-800" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Operator</label>
                            <input type="text" id="operator" value="Test Airlines" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Document Type</label>
                            <select id="documentType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="operator">Operator MEL</option>
                                <option value="master">Master MEL</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Inspector ID</label>
                            <input type="text" id="inspectorId" value="demo-inspector-123" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <button type="submit" id="uploadBtn" 
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50">
                        Upload and Process
                    </button>
                </form>
            </div>

            <!-- Processing Status -->
            <div id="processingStatus" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                    <span class="text-blue-800">Processing documents with OCR...</span>
                </div>
            </div>

            <!-- Results -->
            <div id="results" class="space-y-6"></div>

            <!-- Clear Button -->
            <div class="text-center mt-8">
                <button id="clearBtn" 
                        class="bg-red-600 text-white py-2 px-6 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                    Clear All Data
                </button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (files.length === 0) {
                alert('Please select at least one file');
                return;
            }
            
            const formData = new FormData();
            for (let file of files) {
                formData.append('files', file);
            }
            formData.append('aircraftType', document.getElementById('aircraftType').value);
            formData.append('operator', document.getElementById('operator').value);
            formData.append('documentType', document.getElementById('documentType').value);
            formData.append('inspectorId', document.getElementById('inspectorId').value);
            
            // Show processing status
            document.getElementById('processingStatus').classList.remove('hidden');
            document.getElementById('uploadBtn').disabled = true;
            
            try {
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                displayResults(result);
                
            } catch (error) {
                console.error('Upload failed:', error);
                alert('Upload failed: ' + error.message);
            } finally {
                document.getElementById('processingStatus').classList.add('hidden');
                document.getElementById('uploadBtn').disabled = false;
            }
        });
        
        document.getElementById('clearBtn').addEventListener('click', async () => {
            try {
                await fetch(`${API_BASE}/upload/clear`, { method: 'POST' });
                document.getElementById('results').innerHTML = '';
                document.getElementById('fileInput').value = '';
                alert('All data cleared successfully');
            } catch (error) {
                console.error('Clear failed:', error);
                alert('Clear failed: ' + error.message);
            }
        });
        
        function displayResults(result) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            if (result.results) {
                result.results.forEach(item => {
                    const card = createResultCard(item);
                    resultsDiv.appendChild(card);
                });
            }
        }
        
        function createResultCard(item) {
            const div = document.createElement('div');
            const isLargeDoc = item.parsing.stats?.processingNote;
            
            div.className = `rounded-lg shadow-lg p-6 ${isLargeDoc ? 'processing-note' : 'success-card'}`;
            
            div.innerHTML = `
                <div class="flex items-start justify-between mb-4">
                    <h3 class="text-xl font-semibold text-gray-900">${item.document.originalName}</h3>
                    <span class="px-3 py-1 rounded-full text-sm font-medium ${
                        item.parsing.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }">
                        ${item.parsing.success ? 'Processed' : 'Failed'}
                    </span>
                </div>
                
                ${isLargeDoc ? `
                    <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="font-medium text-yellow-800">Large Document Processing</span>
                        </div>
                        <p class="text-yellow-700 text-sm">${item.parsing.stats.processingNote}</p>
                    </div>
                ` : ''}
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">${item.parsing.pageCount}</div>
                        <div class="text-sm text-gray-600">Total Pages</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">${item.parsing.clausesExtracted}</div>
                        <div class="text-sm text-gray-600">MEL Clauses</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">${item.parsing.confidence}%</div>
                        <div class="text-sm text-gray-600">OCR Confidence</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">${Math.round(item.document.size / 1024)}KB</div>
                        <div class="text-sm text-gray-600">File Size</div>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h4 class="font-medium text-gray-900 mb-2">Document Metadata</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div><span class="font-medium">Aircraft:</span> ${item.document.metadata.aircraftType}</div>
                        <div><span class="font-medium">Operator:</span> ${item.document.metadata.operator}</div>
                        <div><span class="font-medium">Type:</span> ${item.document.metadata.documentType}</div>
                        <div><span class="font-medium">Inspector:</span> ${item.document.metadata.inspectorId}</div>
                    </div>
                </div>
                
                <details class="mt-4">
                    <summary class="cursor-pointer font-medium text-gray-900 hover:text-blue-600">
                        View Extracted Text Preview
                    </summary>
                    <div class="mt-2 p-3 bg-gray-50 rounded-md">
                        <pre class="text-sm text-gray-700 whitespace-pre-wrap">${item.parsing.extractedText}</pre>
                    </div>
                </details>
            `;
            
            return div;
        }
    </script>
</body>
</html>
