# MELynx Supabase Setup Guide

## 🚀 Quick Setup Instructions

### 1. **Apply the Database Schema**

1. **Open Supabase Dashboard**
   - Go to: https://tvjqynnegyrovgugnmix.supabase.co
   - Navigate to **SQL Editor** in the left sidebar

2. **Run the Schema Script**
   - Copy the entire contents of `supabase_schema.sql`
   - Paste into the SQL Editor
   - Click **"Run"** to execute the schema

### 2. **Verify Schema Creation**

After running the script, you should see:

✅ **Tables Created:**
- `mel_documents` - Document storage and metadata
- `mel_evaluations` - Analysis records and results
- `clause_comparisons` - Detailed clause-by-clause comparisons
- `compliance_issues` - Individual compliance findings
- `mel_audit_log` - Complete audit trail
- `analysis_results` - Structured analysis data
- `generated_reports` - Report generation tracking

✅ **Storage Buckets:**
- `melynx-documents` - For uploaded MEL files
- `melynx-reports` - For generated PDF/JSON reports

✅ **Sample Data:**
- Master MEL document (Boeing 737 MMEL)
- Operator MEL document (Sample Airlines)

### 3. **Configure Storage Policies**

The schema automatically sets up:
- Row Level Security (RLS) on all tables
- Storage policies for authenticated users
- Audit trail logging
- Performance indexes

### 4. **Test the Setup**

You can verify the setup by running these queries in the SQL Editor:

```sql
-- Check tables
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name LIKE 'mel_%';

-- Check sample data
SELECT id, original_name, status, metadata->>'aircraftType' as aircraft_type 
FROM mel_documents;

-- Check storage buckets
SELECT * FROM storage.buckets WHERE name LIKE 'melynx%';
```

## 🔧 **Environment Configuration**

The `.env` file has been created with your Supabase credentials:

```env
SUPABASE_URL=https://tvjqynnegyrovgugnmix.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
OPENROUTER_API_KEY=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
```

## 🎯 **Next Steps**

1. **Apply the schema** using the SQL Editor
2. **Start the backend server**: `cd backend && npm run dev`
3. **Start the frontend**: `cd frontend && npm run dev`
4. **Test the full system** by uploading documents and running analyses

## 📊 **Database Schema Overview**

### **Core Tables:**

1. **`mel_documents`** - Document Management
   - File storage and metadata
   - Processing status tracking
   - Extracted text and parsed clauses

2. **`mel_evaluations`** - Analysis Management
   - Links operator and master MELs
   - Stores compliance results
   - Inspector review workflow

3. **`clause_comparisons`** - Detailed Comparisons
   - Clause-by-clause analysis
   - Similarity scoring
   - Difference tracking

4. **`compliance_issues`** - Issue Tracking
   - Critical issues and warnings
   - Severity classification
   - Resolution tracking

5. **`mel_audit_log`** - Audit Trail
   - Complete action history
   - Regulatory compliance
   - User activity tracking

### **Key Features:**

- ✅ **Full Text Search** with pg_trgm extension
- ✅ **JSON Storage** for flexible metadata
- ✅ **Row Level Security** for data protection
- ✅ **Automatic Timestamps** with triggers
- ✅ **Performance Indexes** for fast queries
- ✅ **Storage Integration** for file management
- ✅ **Audit Trail** for regulatory compliance

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **Permission Errors**
   - Ensure you're logged into the correct Supabase project
   - Check that RLS policies are properly configured

2. **Storage Issues**
   - Verify storage buckets are created
   - Check storage policies for authenticated users

3. **Connection Issues**
   - Verify environment variables are correct
   - Check network connectivity to Supabase

### **Support:**

If you encounter any issues:
1. Check the Supabase logs in the dashboard
2. Verify the schema was applied completely
3. Test individual components step by step

## 🎉 **Ready for Production**

Once the schema is applied, MELynx will have:
- ✅ Complete database structure
- ✅ File storage capabilities
- ✅ Audit trail compliance
- ✅ Performance optimization
- ✅ Security policies
- ✅ Sample data for testing

**Status: Ready to run the full MELynx system!**
