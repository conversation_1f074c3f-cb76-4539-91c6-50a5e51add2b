import express from 'express'
import cors from 'cors'
import multer from 'multer'
import { promises as fs, existsSync, readdirSync, unlinkSync, statSync, rmSync } from 'fs'
import path from 'path'

const app = express()
const PORT = process.env.PORT || 3001

// Mock database
const mockDocuments = []
const mockAnalyses = []

// Initialize server
async function initializeServer() {
  // Ensure temp directories exist
  await fs.mkdir('./temp/uploads/', { recursive: true })
  await fs.mkdir('./temp/ocr/', { recursive: true })
  console.log('📁 Temp directories created')
}

// Middleware
app.use(cors())
app.use(express.json())

// Configure multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, './temp/uploads/')
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
      cb(null, file.fieldname + '-' + uniqueSuffix + '-' + file.originalname)
    }
  }),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 10
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'application/xml',
      'text/xml'
    ]
    
    if (allowedTypes.includes(file.mimetype)) {
      console.log(`File upload attempt: ${file.originalname} Type: ${file.mimetype}`)
      cb(null, true)
    } else {
      cb(new Error(`Invalid file type: ${file.mimetype}. Only PDF, DOC, DOCX, TXT, and XML files are allowed.`))
    }
  }
})

// Basic health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'MELynx Backend is running',
    timestamp: new Date().toISOString(),
    ocr_status: 'disabled_for_demo'
  })
})

// Upload route with mock OCR processing
app.post('/api/upload', (req, res) => {
  upload.array('files', 10)(req, res, async (err) => {
    if (err) {
      console.error('Multer error:', err)
      return res.status(400).json({
        error: 'Upload failed',
        message: err.message || 'File upload error'
      })
    }
  try {
    const files = req.files
    const { aircraftType, operator, documentType, inspectorId } = req.body

    console.log('Upload request received:', {
      filesCount: files?.length || 0,
      aircraftType,
      operator,
      documentType,
      inspectorId
    })

    // Validate input
    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select at least one MEL document to upload'
      })
    }

    // Process files with mock OCR
    const uploadResults = []

    for (const file of files) {
      try {
        console.log(`🔍 Processing ${file.originalname} with mock OCR...`)

        // Mock OCR result with page limiting simulation
        const mockOcrResult = {
          totalPages: file.originalname.includes('large') ? 284 : 7, // Simulate large vs small docs
          totalCharacters: 1152,
          averageConfidence: 95,
          fullText: `[MOCK OCR RESULT]\n\nProcessed: ${file.originalname}\nFile size: ${file.size} bytes\n\nThis is a mock OCR result for demonstration.\n\nMEL CLAUSES DETECTED:\n21-31-01: Engine Fire Detection\n21-31-02: Engine Fire Extinguishing\n22-11-01: Autoflight System\n22-11-02: Flight Director\n\nAIRCRAFT TYPE: ${aircraftType}\nOPERATOR: ${operator}`,
          melData: {
            aircraftType: aircraftType,
            operator: operator,
            clauses: [
              { id: '21-31-01', title: 'Engine Fire Detection', status: 'active' },
              { id: '21-31-02', title: 'Engine Fire Extinguishing', status: 'active' },
              { id: '22-11-01', title: 'Autoflight System', status: 'active' },
              { id: '22-11-02', title: 'Flight Director', status: 'active' }
            ],
            sections: ['21', '22']
          },
          processingInfo: file.originalname.includes('large') ? {
            totalPagesInDocument: 284,
            pagesProcessed: 10,
            processingNote: 'Large document detected (284 pages). Smart sampling 10 representative pages: 1, 2, 3, 71, 142, 213, 282, 283, 284'
          } : null
        }

        const document = {
          id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          originalName: file.originalname,
          filename: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
          status: 'parsed',
          metadata: {
            aircraftType,
            operator,
            documentType,
            inspectorId
          },
          uploadedAt: new Date().toISOString(),
          ocrData: mockOcrResult
        }

        // Store document in mock database
        mockDocuments.push(document)

        uploadResults.push({
          document,
          parsing: {
            success: true,
            clausesExtracted: mockOcrResult.melData.clauses.length,
            pageCount: mockOcrResult.totalPages,
            confidence: mockOcrResult.averageConfidence,
            extractedText: mockOcrResult.fullText.substring(0, 500) + '...', // Preview
            stats: {
              totalClauses: mockOcrResult.melData.clauses.length,
              sections: mockOcrResult.melData.sections,
              totalCharacters: mockOcrResult.totalCharacters,
              ocrConfidence: mockOcrResult.averageConfidence,
              processingNote: mockOcrResult.processingInfo?.processingNote || null
            }
          }
        })

        console.log(`✅ Successfully processed ${file.originalname} (mock)`)

      } catch (error) {
        console.error(`❌ Failed to process ${file.originalname}:`, error)

        // Fallback to basic document info
        const document = {
          id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          originalName: file.originalname,
          filename: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
          status: 'error',
          metadata: {
            aircraftType,
            operator,
            documentType,
            inspectorId
          },
          uploadedAt: new Date().toISOString(),
          error: error.message
        }

        mockDocuments.push(document)

        uploadResults.push({
          document,
          parsing: {
            success: false,
            error: error.message
          }
        })
      }
    }

    res.json({
      message: `Successfully uploaded ${files.length} file(s)`,
      results: uploadResults,
      errors: [],
      summary: {
        totalFiles: files.length,
        successful: files.length,
        failed: 0,
        totalSize: files.reduce((sum, file) => sum + file.size, 0)
      }
    })

  } catch (error) {
    console.error('Upload error:', error)
    res.status(500).json({
      error: 'Upload failed',
      message: error.message || 'An error occurred during file upload'
    })
  }
  }) // Close the multer error handler
})

// Clear uploads endpoint
app.post('/api/upload/clear', async (req, res) => {
  try {
    const uploadsDir = './temp/uploads';
    const ocrDir = './temp/ocr';
    
    // Clear uploads directory
    if (existsSync(uploadsDir)) {
      const files = readdirSync(uploadsDir);
      for (const file of files) {
        unlinkSync(path.join(uploadsDir, file));
      }
    }
    
    // Clear OCR temp directory
    if (existsSync(ocrDir)) {
      const files = readdirSync(ocrDir);
      for (const file of files) {
        const filePath = path.join(ocrDir, file);
        if (statSync(filePath).isDirectory()) {
          rmSync(filePath, { recursive: true, force: true });
        } else {
          unlinkSync(filePath);
        }
      }
    }
    
    // Clear mock databases
    mockDocuments.length = 0;
    mockAnalyses.length = 0;
    
    console.log('🧹 Cleared uploads, temp files, and mock databases');
    res.json({ success: true, message: 'All files and data cleared' });
    
  } catch (error) {
    console.error('❌ Clear failed:', error);
    res.status(500).json({ 
      error: 'Clear failed', 
      details: error.message 
    });
  }
});

// Start server
initializeServer().then(() => {
  app.listen(PORT, () => {
    console.log(`🚀 MELynx Simple Backend running on port ${PORT}`)
    console.log(`📋 Health check: http://localhost:${PORT}/api/health`)
    console.log(`🔧 OCR: Mock mode (for demonstration)`)
  })
}).catch(error => {
  console.error('❌ Failed to initialize server:', error)
  process.exit(1)
})
