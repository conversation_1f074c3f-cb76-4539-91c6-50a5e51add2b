import PDFDocument from 'pdfkit';
import fs from 'fs';

// Create a new PDF document
const doc = new PDFDocument();

// Pipe the PDF to a file
doc.pipe(fs.createWriteStream('test-mel.pdf'));

// Add content to the PDF
doc.fontSize(16)
   .text('BOEING 737-800 MINIMUM EQUIPMENT LIST', 50, 50);

doc.fontSize(12)
   .text('OPERATOR: Test Airlines', 50, 100)
   .text('AIRCRAFT TYPE: Boeing 737-800', 50, 120)
   .text('REVISION: 15', 50, 140)
   .text('EFFECTIVE DATE: 01/15/2024', 50, 160);

doc.fontSize(14)
   .text('SECTION 21 - AIR CONDITIONING', 50, 200);

doc.fontSize(12)
   .text('21-31-01 Air Conditioning Pack', 50, 230)
   .text('(A) One may be inoperative provided:', 50, 250)
   .text('    a) Flight is conducted in accordance with the following procedures', 70, 270)
   .text('    b) Maximum cabin altitude does not exceed 10,000 feet', 70, 290);

doc.text('21-31-02 Pack Flow Control Valve', 50, 320)
   .text('(B) One may be inoperative provided:', 50, 340)
   .text('    a) Associated pack is operative', 70, 360)
   .text('    b) Flight time does not exceed 3 hours', 70, 380);

doc.fontSize(14)
   .text('SECTION 22 - AUTO FLIGHT', 50, 420);

doc.fontSize(12)
   .text('22-11-01 Autopilot System', 50, 450)
   .text('(C) May be inoperative provided:', 50, 470)
   .text('    a) Flight is conducted under VFR conditions', 70, 490)
   .text('    b) Pilot workload permits manual flight', 70, 510);

doc.text('22-11-02 Flight Director', 50, 540)
   .text('(D) One may be inoperative provided:', 50, 560)
   .text('    a) Other flight director is operative', 70, 580)
   .text('    b) Crew is trained for single FD operations', 70, 600);

doc.fontSize(14)
   .text('SECTION 23 - COMMUNICATIONS', 50, 640);

doc.fontSize(12)
   .text('23-11-01 VHF Communication System', 50, 670)
   .text('(A) One may be inoperative provided:', 50, 690)
   .text('    a) Remaining system is operative', 70, 710)
   .text('    b) Flight operates in controlled airspace', 70, 730);

doc.text('23-11-02 HF Communication System', 50, 760)
   .text('(B) May be inoperative for domestic flights provided:', 50, 780)
   .text('    a) VHF coverage is available', 70, 800)
   .text('    b) Flight does not exceed 4 hours', 70, 820);

// Finalize the PDF
doc.end();

console.log('✅ Test PDF created: test-mel.pdf');
