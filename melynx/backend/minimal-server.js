#!/usr/bin/env node

import dotenv from 'dotenv'
dotenv.config()

import express from 'express'
import cors from 'cors'

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())

// Basic health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'MELynx Backend is running',
    timestamp: new Date().toISOString(),
    environment: {
      supabase_configured: !!process.env.SUPABASE_URL,
      openrouter_configured: !!process.env.OPENROUTER_API_KEY
    }
  })
})

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'MELynx API Test Successful',
    config: {
      supabase_url: process.env.SUPABASE_URL ? 'configured' : 'missing',
      openrouter_key: process.env.OPENROUTER_API_KEY ? 'configured' : 'missing'
    }
  })
})

// Mock analysis endpoint for demo
app.get('/api/analysis/list', (req, res) => {
  res.json({
    analyses: [
      {
        id: 'demo-analysis-001',
        status: 'completed',
        startedAt: '2024-01-15T10:30:00Z',
        completedAt: '2024-01-15T10:33:45Z',
        results: {
          complianceRate: 82.05,
          criticalIssues: [
            { title: 'Main Landing Gear Door Category Conflict', severity: 'critical' },
            { title: 'Missing Navigation Light Requirements', severity: 'critical' }
          ],
          warnings: [
            { title: 'Elevator Tab Maintenance Interval Modified', severity: 'high' }
          ]
        },
        operatorDocument: {
          originalName: 'Sample_Airlines_737_MEL.pdf',
          metadata: {
            aircraftType: 'Boeing 737-800',
            operator: 'Sample Airlines'
          }
        }
      }
    ]
  })
})

// Mock analysis results endpoint
app.get('/api/analysis/results/:id', (req, res) => {
  res.json({
    status: 'completed',
    results: {
      complianceRate: 82.05,
      totalClauses: 156,
      matchedClauses: 128,
      missingClauses: 28,
      modifiedClauses: 12,
      additionalClauses: 2,
      criticalIssues: [
        {
          title: 'Main Landing Gear Door Category Conflict',
          description: 'Operator MEL classifies clause 32-41-01 as Category B while Master MEL requires Category A.',
          recommendation: 'Immediately align with Master MEL Category A classification'
        },
        {
          title: 'Missing Navigation Light Requirements',
          description: 'Critical navigation lighting clause absent from Operator MEL.',
          recommendation: 'Add clause 34-11-01 to Operator MEL with appropriate operational limitations'
        }
      ],
      warnings: [
        {
          title: 'Elevator Tab Maintenance Interval Modified',
          description: 'Operator MEL extends maintenance interval from 3 to 5 flight cycles.'
        }
      ],
      recommendations: [
        'Immediately address the Category A/B conflict in clause 32-41-01',
        'Add missing navigation light clause 34-11-01 with appropriate limitations',
        'Review and justify all maintenance interval modifications'
      ],
      summary: 'MEL compliance analysis reveals significant compliance gaps requiring immediate attention. While 82% of clauses are compliant, critical safety-related discrepancies in flight control and landing gear systems pose operational risks.',
      clauseComparisons: []
    }
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 MELynx Backend running on port ${PORT}`)
  console.log(`📋 Health check: http://localhost:${PORT}/api/health`)
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`)
  console.log(`🔧 Environment:`)
  console.log(`   SUPABASE_URL: ${process.env.SUPABASE_URL ? '✅ SET' : '❌ NOT SET'}`)
  console.log(`   OPENROUTER_API_KEY: ${process.env.OPENROUTER_API_KEY ? '✅ SET' : '❌ NOT SET'}`)
})
