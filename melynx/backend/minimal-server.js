#!/usr/bin/env node

import dotenv from 'dotenv'
dotenv.config()

import express from 'express'
import cors from 'cors'
import multer from 'multer'
import { promises as fs } from 'fs'
import OCRService from './services/ocrService.js'

const app = express()
const PORT = process.env.PORT || 3001

// Initialize OCR service
const ocrService = new OCRService()

// Mock database
const mockDocuments = []
const mockAnalyses = []

// Initialize server
async function initializeServer() {
  // Ensure temp directories exist
  await fs.mkdir('./temp/uploads/', { recursive: true })
  await fs.mkdir('./temp/ocr/', { recursive: true })
  console.log('📁 Temp directories created')
}

// Middleware
app.use(cors())
app.use(express.json())

// Configure multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, './temp/uploads/')
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
      cb(null, file.fieldname + '-' + uniqueSuffix + '-' + file.originalname)
    }
  }),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 10
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'application/xml',
      'text/xml'
    ]
    console.log('File upload attempt:', file.originalname, 'Type:', file.mimetype)
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error(`Invalid file type: ${file.mimetype}. Only PDF, DOC, DOCX, TXT, and XML files are allowed.`))
    }
  }
})

// Basic health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'MELynx Backend is running',
    timestamp: new Date().toISOString(),
    environment: {
      supabase_configured: !!process.env.SUPABASE_URL,
      openrouter_configured: !!process.env.OPENROUTER_API_KEY
    }
  })
})

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'MELynx API Test Successful',
    config: {
      supabase_url: process.env.SUPABASE_URL ? 'configured' : 'missing',
      openrouter_key: process.env.OPENROUTER_API_KEY ? 'configured' : 'missing'
    }
  })
})

// Upload route with error handling
app.post('/api/upload', (req, res) => {
  upload.array('files', 10)(req, res, async (err) => {
    if (err) {
      console.error('Multer error:', err)
      return res.status(400).json({
        error: 'Upload failed',
        message: err.message || 'File upload error'
      })
    }
  try {
    const files = req.files
    const { aircraftType, operator, documentType, inspectorId } = req.body

    console.log('Upload request received:', {
      filesCount: files?.length || 0,
      aircraftType,
      operator,
      documentType,
      inspectorId
    })

    // Validate input
    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select at least one MEL document to upload'
      })
    }

    // Process files with OCR
    const uploadResults = []

    for (const file of files) {
      try {
        console.log(`🔍 Processing ${file.originalname} with OCR...`)

        // Perform OCR processing
        const ocrResult = await ocrService.processDocument(file.path)

        // Extract metadata from OCR or use provided values
        const extractedAircraftType = ocrResult.melData.aircraftType || aircraftType
        const extractedOperator = ocrResult.melData.operator || operator

        const document = {
          id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          originalName: file.originalname,
          filename: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
          status: 'parsed',
          metadata: {
            aircraftType: extractedAircraftType,
            operator: extractedOperator,
            documentType,
            inspectorId
          },
          uploadedAt: new Date().toISOString(),
          ocrData: ocrResult
        }

        // Store document in mock database
        mockDocuments.push(document)

        uploadResults.push({
          document,
          parsing: {
            success: true,
            clausesExtracted: ocrResult.melData.clauses.length,
            pageCount: ocrResult.totalPages,
            confidence: ocrResult.averageConfidence,
            extractedText: ocrResult.fullText.substring(0, 500) + '...', // Preview
            stats: {
              totalClauses: ocrResult.melData.clauses.length,
              sections: ocrResult.melData.sections,
              totalCharacters: ocrResult.totalCharacters,
              ocrConfidence: ocrResult.averageConfidence
            }
          }
        })

        console.log(`✅ Successfully processed ${file.originalname}`)

      } catch (error) {
        console.error(`❌ Failed to process ${file.originalname}:`, error)

        // Fallback to basic document info without OCR
        const document = {
          id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          originalName: file.originalname,
          filename: file.originalname,
          size: file.size,
          mimeType: file.mimetype,
          status: 'error',
          metadata: {
            aircraftType,
            operator,
            documentType,
            inspectorId
          },
          uploadedAt: new Date().toISOString(),
          error: error.message
        }

        mockDocuments.push(document)

        uploadResults.push({
          document,
          parsing: {
            success: false,
            error: error.message,
            clausesExtracted: 0,
            pageCount: 0
          }
        })
      }
    }

    // Clean up uploaded files after processing
    for (const file of files) {
      try {
        if (file.path) {
          await fs.unlink(file.path)
        }
      } catch (cleanupError) {
        console.warn(`⚠️ Failed to cleanup file ${file.originalname}:`, cleanupError.message)
      }
    }

    res.json({
      message: `Successfully uploaded ${files.length} file(s)`,
      results: uploadResults,
      errors: [],
      summary: {
        totalFiles: files.length,
        successful: files.length,
        failed: 0,
        totalSize: files.reduce((sum, file) => sum + file.size, 0)
      }
    })

  } catch (error) {
    console.error('Upload error:', error)
    res.status(500).json({
      error: 'Upload failed',
      message: error.message || 'An error occurred during file upload'
    })
  }
  }) // Close the multer error handler
})

// Start analysis processing
app.post('/api/analysis/start', async (req, res) => {
  try {
    const { operatorMelId, masterMelId, createdBy, options } = req.body

    console.log('Analysis start request:', { operatorMelId, masterMelId, createdBy, options })

    // Validate required fields
    if (!operatorMelId || !masterMelId || !createdBy) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'operatorMelId, masterMelId, and createdBy are required'
      })
    }

    // Look up the actual documents
    const operatorDoc = mockDocuments.find(doc => doc.id === operatorMelId)
    const masterDoc = mockDocuments.find(doc => doc.id === masterMelId)

    if (!operatorDoc || !masterDoc) {
      return res.status(404).json({
        error: 'Documents not found',
        message: 'One or both of the specified documents could not be found'
      })
    }

    // Mock analysis ID
    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Store the analysis in our mock database
    mockAnalyses.push({
      id: analysisId,
      operatorMelId,
      masterMelId,
      status: 'initiated',
      startedAt: new Date().toISOString(),
      completedAt: null,
      results: null,
      options,
      createdBy,
      operatorDocument: operatorDoc,
      masterDocument: masterDoc
    })

    // Simulate processing completion after 3 seconds
    setTimeout(() => {
      const analysis = mockAnalyses.find(a => a.id === analysisId)
      if (analysis) {
        analysis.status = 'completed'
        analysis.completedAt = new Date().toISOString()
        analysis.results = {
          complianceRate: 82.05,
          criticalIssues: [
            {
              title: "Main Landing Gear Door Category Conflict",
              severity: "critical"
            },
            {
              title: "Missing Navigation Light Requirements",
              severity: "critical"
            }
          ],
          warnings: [
            {
              title: "Elevator Tab Maintenance Interval Modified",
              severity: "high"
            }
          ]
        }
      }
    }, 3000)

    res.json({
      message: 'MEL analysis started successfully',
      analysis: {
        id: analysisId,
        status: 'initiated',
        operatorMelId,
        masterMelId,
        startedAt: new Date().toISOString(),
        options
      },
      estimatedTime: '3-5 minutes'
    })

  } catch (error) {
    console.error('Analysis start error:', error)
    res.status(500).json({
      error: 'Analysis failed to start',
      message: error.message || 'An error occurred while starting analysis'
    })
  }
})

// List analyses endpoint
app.get('/api/analysis/list', (req, res) => {
  const { createdBy, status, page = '1', limit = '20' } = req.query

  let filteredAnalyses = [...mockAnalyses]

  // Filter by createdBy if provided
  if (createdBy) {
    filteredAnalyses = filteredAnalyses.filter(a => a.createdBy === createdBy)
  }

  // Filter by status if provided
  if (status) {
    filteredAnalyses = filteredAnalyses.filter(a => a.status === status)
  }

  // Add default demo analysis if no analyses exist
  if (filteredAnalyses.length === 0) {
    filteredAnalyses = [
      {
        id: 'demo-analysis-001',
        status: 'completed',
        startedAt: '2024-01-15T10:30:00Z',
        completedAt: '2024-01-15T10:33:45Z',
        results: {
          complianceRate: 82.05,
          criticalIssues: [
            { title: 'Main Landing Gear Door Category Conflict', severity: 'critical' },
            { title: 'Missing Navigation Light Requirements', severity: 'critical' }
          ],
          warnings: [
            { title: 'Elevator Tab Maintenance Interval Modified', severity: 'high' }
          ]
        },
        operatorDocument: {
          originalName: 'Sample_Airlines_737_MEL.pdf',
          metadata: {
            aircraftType: 'Boeing 737-800',
            operator: 'Sample Airlines'
          }
        }
      }
    ]
  }

  const pageNum = parseInt(page)
  const limitNum = parseInt(limit)
  const offset = (pageNum - 1) * limitNum

  const paginatedAnalyses = filteredAnalyses.slice(offset, offset + limitNum)

  res.json({
    analyses: paginatedAnalyses,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: filteredAnalyses.length,
      hasMore: filteredAnalyses.length > offset + limitNum
    }
  })
})

// Analysis results endpoint
app.get('/api/analysis/results/:id', (req, res) => {
  const { id } = req.params

  // Find the analysis in our mock database
  const analysis = mockAnalyses.find(a => a.id === id)

  if (!analysis) {
    return res.status(404).json({
      error: 'Analysis not found',
      message: `Analysis with ID ${id} could not be found`
    })
  }

  // Return the analysis with enhanced results
  const enhancedResults = {
    ...analysis.results,
    totalClauses: 156,
    matchedClauses: 128,
    missingClauses: 28,
    modifiedClauses: 12,
    additionalClauses: 2,
    criticalIssues: analysis.results?.criticalIssues?.map(issue => ({
      ...issue,
      description: issue.title === 'Main Landing Gear Door Category Conflict'
        ? 'Operator MEL classifies clause 32-41-01 as Category B while Master MEL requires Category A.'
        : 'Critical navigation lighting clause absent from Operator MEL.',
      recommendation: issue.title === 'Main Landing Gear Door Category Conflict'
        ? 'Immediately align with Master MEL Category A classification'
        : 'Add clause 34-11-01 to Operator MEL with appropriate operational limitations'
    })) || [],
    warnings: analysis.results?.warnings?.map(warning => ({
      ...warning,
      description: 'Operator MEL extends maintenance interval from 3 to 5 flight cycles.'
    })) || [],
    recommendations: [
      'Immediately address the Category A/B conflict in clause 32-41-01',
      'Add missing navigation light clause 34-11-01 with appropriate limitations',
      'Review and justify all maintenance interval modifications'
    ],
    summary: `MEL compliance analysis reveals significant compliance gaps requiring immediate attention. While ${analysis.results?.complianceRate || 82}% of clauses are compliant, critical safety-related discrepancies in flight control and landing gear systems pose operational risks.`,
    clauseComparisons: []
  }

  res.json({
    id: analysis.id,
    status: analysis.status,
    startedAt: analysis.startedAt,
    completedAt: analysis.completedAt,
    operatorDocument: analysis.operatorDocument,
    masterDocument: analysis.masterDocument,
    results: enhancedResults
  })
})

// Start server
async function startServer() {
  await initializeServer()

  app.listen(PORT, () => {
    console.log(`🚀 MELynx Backend running on port ${PORT}`)
    console.log(`📋 Health check: http://localhost:${PORT}/api/health`)
    console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`)
    console.log(`🔧 Environment:`)
    console.log(`   SUPABASE_URL: ${process.env.SUPABASE_URL ? '✅ SET' : '❌ NOT SET'}`)
    console.log(`   OPENROUTER_API_KEY: ${process.env.OPENROUTER_API_KEY ? '✅ SET' : '❌ NOT SET'}`)
  })
}

startServer().catch(console.error)
