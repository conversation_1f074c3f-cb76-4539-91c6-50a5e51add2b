#!/usr/bin/env node

import dotenv from 'dotenv'
dotenv.config()

import express from 'express'
import cors from 'cors'
import multer from 'multer'

const app = express()
const PORT = process.env.PORT || 3001

// Mock database
const mockAnalyses = []

// Middleware
app.use(cors())
app.use(express.json())

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 10
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'application/xml',
      'text/xml'
    ]
    console.log('File upload attempt:', file.originalname, 'Type:', file.mimetype)
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error(`Invalid file type: ${file.mimetype}. Only PDF, DOC, DOCX, TXT, and XML files are allowed.`))
    }
  }
})

// Basic health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'MELynx Backend is running',
    timestamp: new Date().toISOString(),
    environment: {
      supabase_configured: !!process.env.SUPABASE_URL,
      openrouter_configured: !!process.env.OPENROUTER_API_KEY
    }
  })
})

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'MELynx API Test Successful',
    config: {
      supabase_url: process.env.SUPABASE_URL ? 'configured' : 'missing',
      openrouter_key: process.env.OPENROUTER_API_KEY ? 'configured' : 'missing'
    }
  })
})

// Upload route with error handling
app.post('/api/upload', (req, res) => {
  upload.array('files', 10)(req, res, async (err) => {
    if (err) {
      console.error('Multer error:', err)
      return res.status(400).json({
        error: 'Upload failed',
        message: err.message || 'File upload error'
      })
    }
  try {
    const files = req.files
    const { aircraftType, operator, documentType, inspectorId } = req.body

    console.log('Upload request received:', {
      filesCount: files?.length || 0,
      aircraftType,
      operator,
      documentType,
      inspectorId
    })

    // Validate input
    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select at least one MEL document to upload'
      })
    }

    // For now, just return success with file info (matching expected format)
    const uploadResults = files.map(file => ({
      document: {
        id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        originalName: file.originalname,
        filename: file.originalname,
        size: file.size,
        mimeType: file.mimetype,
        status: 'parsed',
        metadata: {
          aircraftType,
          operator,
          documentType,
          inspectorId
        },
        uploadedAt: new Date().toISOString()
      },
      parsing: {
        success: true,
        clausesExtracted: Math.floor(Math.random() * 50) + 10, // Mock data
        pageCount: Math.floor(Math.random() * 20) + 5,
        stats: {
          totalClauses: Math.floor(Math.random() * 50) + 10,
          sectionCounts: {
            '21': Math.floor(Math.random() * 10) + 1,
            '22': Math.floor(Math.random() * 8) + 1,
            '23': Math.floor(Math.random() * 12) + 1
          },
          categoryCounts: {
            'A': Math.floor(Math.random() * 15) + 5,
            'B': Math.floor(Math.random() * 20) + 10,
            'C': Math.floor(Math.random() * 10) + 3,
            'D': Math.floor(Math.random() * 5) + 1
          }
        }
      }
    }))

    res.json({
      message: `Successfully uploaded ${files.length} file(s)`,
      results: uploadResults,
      errors: [],
      summary: {
        totalFiles: files.length,
        successful: files.length,
        failed: 0,
        totalSize: files.reduce((sum, file) => sum + file.size, 0)
      }
    })

  } catch (error) {
    console.error('Upload error:', error)
    res.status(500).json({
      error: 'Upload failed',
      message: error.message || 'An error occurred during file upload'
    })
  }
  }) // Close the multer error handler
})

// Start analysis processing
app.post('/api/analysis/start', async (req, res) => {
  try {
    const { operatorMelId, masterMelId, createdBy, options } = req.body

    console.log('Analysis start request:', { operatorMelId, masterMelId, createdBy, options })

    // Validate required fields
    if (!operatorMelId || !masterMelId || !createdBy) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'operatorMelId, masterMelId, and createdBy are required'
      })
    }

    // Mock analysis ID
    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Store the analysis in our mock database
    mockAnalyses.push({
      id: analysisId,
      operatorMelId,
      masterMelId,
      status: 'initiated',
      startedAt: new Date().toISOString(),
      completedAt: null,
      results: null,
      options,
      createdBy
    })

    // Simulate processing completion after 3 seconds
    setTimeout(() => {
      const analysis = mockAnalyses.find(a => a.id === analysisId)
      if (analysis) {
        analysis.status = 'completed'
        analysis.completedAt = new Date().toISOString()
        analysis.results = {
          complianceRate: 82.05,
          criticalIssues: [
            {
              title: "Main Landing Gear Door Category Conflict",
              severity: "critical"
            },
            {
              title: "Missing Navigation Light Requirements",
              severity: "critical"
            }
          ],
          warnings: [
            {
              title: "Elevator Tab Maintenance Interval Modified",
              severity: "high"
            }
          ]
        }
      }
    }, 3000)

    res.json({
      message: 'MEL analysis started successfully',
      analysis: {
        id: analysisId,
        status: 'initiated',
        operatorMelId,
        masterMelId,
        startedAt: new Date().toISOString(),
        options
      },
      estimatedTime: '3-5 minutes'
    })

  } catch (error) {
    console.error('Analysis start error:', error)
    res.status(500).json({
      error: 'Analysis failed to start',
      message: error.message || 'An error occurred while starting analysis'
    })
  }
})

// List analyses endpoint
app.get('/api/analysis/list', (req, res) => {
  const { createdBy, status, page = '1', limit = '20' } = req.query

  let filteredAnalyses = [...mockAnalyses]

  // Filter by createdBy if provided
  if (createdBy) {
    filteredAnalyses = filteredAnalyses.filter(a => a.createdBy === createdBy)
  }

  // Filter by status if provided
  if (status) {
    filteredAnalyses = filteredAnalyses.filter(a => a.status === status)
  }

  // Add default demo analysis if no analyses exist
  if (filteredAnalyses.length === 0) {
    filteredAnalyses = [
      {
        id: 'demo-analysis-001',
        status: 'completed',
        startedAt: '2024-01-15T10:30:00Z',
        completedAt: '2024-01-15T10:33:45Z',
        results: {
          complianceRate: 82.05,
          criticalIssues: [
            { title: 'Main Landing Gear Door Category Conflict', severity: 'critical' },
            { title: 'Missing Navigation Light Requirements', severity: 'critical' }
          ],
          warnings: [
            { title: 'Elevator Tab Maintenance Interval Modified', severity: 'high' }
          ]
        },
        operatorDocument: {
          originalName: 'Sample_Airlines_737_MEL.pdf',
          metadata: {
            aircraftType: 'Boeing 737-800',
            operator: 'Sample Airlines'
          }
        }
      }
    ]
  }

  const pageNum = parseInt(page)
  const limitNum = parseInt(limit)
  const offset = (pageNum - 1) * limitNum

  const paginatedAnalyses = filteredAnalyses.slice(offset, offset + limitNum)

  res.json({
    analyses: paginatedAnalyses,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: filteredAnalyses.length,
      hasMore: filteredAnalyses.length > offset + limitNum
    }
  })
})

// Mock analysis results endpoint
app.get('/api/analysis/results/:id', (req, res) => {
  res.json({
    status: 'completed',
    results: {
      complianceRate: 82.05,
      totalClauses: 156,
      matchedClauses: 128,
      missingClauses: 28,
      modifiedClauses: 12,
      additionalClauses: 2,
      criticalIssues: [
        {
          title: 'Main Landing Gear Door Category Conflict',
          description: 'Operator MEL classifies clause 32-41-01 as Category B while Master MEL requires Category A.',
          recommendation: 'Immediately align with Master MEL Category A classification'
        },
        {
          title: 'Missing Navigation Light Requirements',
          description: 'Critical navigation lighting clause absent from Operator MEL.',
          recommendation: 'Add clause 34-11-01 to Operator MEL with appropriate operational limitations'
        }
      ],
      warnings: [
        {
          title: 'Elevator Tab Maintenance Interval Modified',
          description: 'Operator MEL extends maintenance interval from 3 to 5 flight cycles.'
        }
      ],
      recommendations: [
        'Immediately address the Category A/B conflict in clause 32-41-01',
        'Add missing navigation light clause 34-11-01 with appropriate limitations',
        'Review and justify all maintenance interval modifications'
      ],
      summary: 'MEL compliance analysis reveals significant compliance gaps requiring immediate attention. While 82% of clauses are compliant, critical safety-related discrepancies in flight control and landing gear systems pose operational risks.',
      clauseComparisons: []
    }
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 MELynx Backend running on port ${PORT}`)
  console.log(`📋 Health check: http://localhost:${PORT}/api/health`)
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`)
  console.log(`🔧 Environment:`)
  console.log(`   SUPABASE_URL: ${process.env.SUPABASE_URL ? '✅ SET' : '❌ NOT SET'}`)
  console.log(`   OPENROUTER_API_KEY: ${process.env.OPENROUTER_API_KEY ? '✅ SET' : '❌ NOT SET'}`)
})
