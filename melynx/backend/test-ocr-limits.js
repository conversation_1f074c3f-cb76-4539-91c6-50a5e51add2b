import OCRService from './services/ocrService.js';
import path from 'path';

async function testOCRLimits() {
  console.log('🧪 Testing OCR page limits...');
  
  const ocrService = new OCRService();
  
  try {
    // Test with small document (should process all pages)
    console.log('\n📄 Testing small document (test-mel.pdf)...');
    const smallResult = await ocrService.processDocument('./test-mel.pdf', {
      maxPages: 50,
      smartSampling: true,
      samplePages: 10
    });
    
    console.log(`✅ Small document result:`, {
      totalPages: smallResult.totalPages,
      totalCharacters: smallResult.totalCharacters,
      averageConfidence: smallResult.averageConfidence
    });
    
    // Test with large document simulation (set very low limits)
    console.log('\n📄 Testing with low page limits (simulating large document)...');
    const limitedResult = await ocrService.processDocument('./test-mel.pdf', {
      maxPages: 3, // Very low limit to test sampling
      smartSampling: true,
      samplePages: 3
    });
    
    console.log(`✅ Limited document result:`, {
      totalPages: limitedResult.totalPages,
      totalCharacters: limitedResult.totalCharacters,
      averageConfidence: limitedResult.averageConfidence,
      processingNote: limitedResult.fullText.includes('[PROCESSING NOTE:') ? 'Has processing note' : 'No processing note'
    });
    
    console.log('\n🎉 OCR limits test completed successfully!');
    
  } catch (error) {
    console.error('❌ OCR limits test failed:', error);
  } finally {
    await ocrService.destroy();
  }
}

testOCRLimits();
