import Tesseract from 'tesseract.js';
import pdf2pic from 'pdf2pic';
import sharp from 'sharp';
import { promises as fs } from 'fs';
import path from 'path';

class OCRService {
  constructor() {
    this.tesseractWorker = null;
    this.initializeWorker();
  }

  async initializeWorker() {
    try {
      this.tesseractWorker = await Tesseract.createWorker('eng', 1, {
        logger: m => console.log('OCR:', m)
      });
      
      // Configure for better MEL document recognition
      await this.tesseractWorker.setParameters({
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-.,()[]{}:;/\\|+= \n\t',
        tessedit_pageseg_mode: Tesseract.PSM.AUTO,
        preserve_interword_spaces: '1'
      });
      
      console.log('✅ OCR Worker initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize OCR worker:', error);
    }
  }

  async processDocument(filePath, options = {}) {
    try {
      console.log(`🔍 Starting OCR processing for: ${filePath}`);
      
      const fileExtension = path.extname(filePath).toLowerCase();
      let imagePaths = [];
      
      if (fileExtension === '.pdf') {
        // Convert PDF to images
        imagePaths = await this.convertPdfToImages(filePath);
      } else if (['.jpg', '.jpeg', '.png', '.tiff'].includes(fileExtension)) {
        // Process image directly
        imagePaths = [filePath];
      } else if (['.txt', '.text'].includes(fileExtension)) {
        // Process text file directly
        return await this.processTextFile(filePath);
      } else {
        throw new Error(`Unsupported file format: ${fileExtension}`);
      }
      
      // Process each page/image
      const results = [];
      for (let i = 0; i < imagePaths.length; i++) {
        console.log(`📄 Processing page ${i + 1}/${imagePaths.length}`);
        const pageResult = await this.processImage(imagePaths[i], i + 1);
        results.push(pageResult);
      }
      
      // Clean up temporary image files if they were created from PDF
      if (fileExtension === '.pdf') {
        await this.cleanupTempFiles(imagePaths);
      }
      
      // Combine and analyze results
      const combinedResult = await this.analyzeExtractedText(results);
      
      console.log(`✅ OCR processing completed. Extracted ${combinedResult.totalCharacters} characters from ${results.length} pages`);
      
      return combinedResult;
      
    } catch (error) {
      console.error('❌ OCR processing failed:', error);
      throw error;
    }
  }

  async convertPdfToImages(pdfPath) {
    try {
      const convert = pdf2pic.fromPath(pdfPath, {
        density: 300,           // High DPI for better OCR
        saveFilename: "page",
        savePath: "./temp/ocr/",
        format: "png",
        width: 2480,           // A4 at 300 DPI
        height: 3508
      });
      
      // Ensure temp directory exists
      await fs.mkdir('./temp/ocr/', { recursive: true });
      
      const results = await convert.bulk(-1); // Convert all pages
      return results.map(result => result.path);
      
    } catch (error) {
      console.error('❌ PDF to image conversion failed:', error);
      throw error;
    }
  }

  async processTextFile(filePath) {
    try {
      console.log(`📄 Processing text file: ${filePath}`);

      // Read text file directly
      const text = await fs.readFile(filePath, 'utf-8');

      // Create a mock OCR result structure
      const result = {
        totalPages: 1,
        totalCharacters: text.length,
        averageConfidence: 100, // Text files have 100% confidence
        fullText: text,
        pages: [{
          pageNumber: 1,
          text: text,
          confidence: 100,
          words: [],
          lines: [],
          paragraphs: [],
          blocks: []
        }],
        melData: this.extractMELData(text),
        extractedAt: new Date().toISOString()
      };

      console.log(`✅ Text file processed successfully. ${text.length} characters extracted`);
      return result;

    } catch (error) {
      console.error('❌ Text file processing failed:', error);
      throw error;
    }
  }

  async processImage(imagePath, pageNumber) {
    try {
      // Enhance image for better OCR
      const enhancedImagePath = await this.enhanceImage(imagePath);
      
      // Perform OCR
      const { data } = await this.tesseractWorker.recognize(enhancedImagePath);
      
      return {
        pageNumber,
        text: data.text,
        confidence: data.confidence,
        words: data.words,
        lines: data.lines,
        paragraphs: data.paragraphs,
        blocks: data.blocks
      };
      
    } catch (error) {
      console.error(`❌ Image processing failed for page ${pageNumber}:`, error);
      throw error;
    }
  }

  async enhanceImage(imagePath) {
    try {
      const enhancedPath = imagePath.replace(/\.(png|jpg|jpeg)$/i, '_enhanced.png');
      
      await sharp(imagePath)
        .greyscale()                    // Convert to grayscale
        .normalize()                    // Normalize contrast
        .sharpen()                      // Sharpen text
        .threshold(128)                 // Apply threshold for better text contrast
        .png({ quality: 100 })
        .toFile(enhancedPath);
      
      return enhancedPath;
      
    } catch (error) {
      console.error('❌ Image enhancement failed:', error);
      return imagePath; // Return original if enhancement fails
    }
  }

  async analyzeExtractedText(pageResults) {
    const allText = pageResults.map(page => page.text).join('\n\n');
    const totalCharacters = allText.length;
    const averageConfidence = pageResults.reduce((sum, page) => sum + page.confidence, 0) / pageResults.length;
    
    // Extract MEL-specific information
    const melData = this.extractMELData(allText);
    
    return {
      totalPages: pageResults.length,
      totalCharacters,
      averageConfidence: Math.round(averageConfidence * 100) / 100,
      fullText: allText,
      pages: pageResults,
      melData,
      extractedAt: new Date().toISOString()
    };
  }

  extractMELData(text) {
    const melData = {
      aircraftType: null,
      operator: null,
      documentTitle: null,
      revision: null,
      effectiveDate: null,
      clauses: [],
      sections: []
    };
    
    // Extract aircraft type (common patterns)
    const aircraftPatterns = [
      /(?:aircraft|a\/c|type)[\s:]+([A-Z0-9\-\s]+(?:boeing|airbus|embraer|bombardier|cessna|gulfstream)[A-Z0-9\-\s]*)/i,
      /(boeing|airbus|embraer|bombardier|cessna|gulfstream)[\s\-]*([A-Z0-9\-]+)/i,
      /([A-Z]{1,3}[\-\s]*\d{2,4}[\-\s]*[A-Z0-9]*)/g
    ];
    
    for (const pattern of aircraftPatterns) {
      const match = text.match(pattern);
      if (match) {
        melData.aircraftType = match[1] || match[0];
        break;
      }
    }
    
    // Extract operator/airline
    const operatorPatterns = [
      /(?:operator|airline|carrier)[\s:]+([A-Z][A-Za-z\s&]+)/i,
      /([A-Z][A-Za-z\s&]+)[\s]+(?:airlines?|airways?|aviation)/i
    ];
    
    for (const pattern of operatorPatterns) {
      const match = text.match(pattern);
      if (match) {
        melData.operator = match[1].trim();
        break;
      }
    }
    
    // Extract MEL clauses (format: XX-XX-XX)
    const clausePattern = /(\d{2})-(\d{2})-(\d{2})/g;
    const clauses = [...text.matchAll(clausePattern)];
    melData.clauses = clauses.map(match => ({
      full: match[0],
      section: match[1],
      subsection: match[2],
      item: match[3]
    }));
    
    // Extract sections (21, 22, 23, etc.)
    const sectionPattern = /(?:section|sec\.?)\s*(\d{2})/gi;
    const sections = [...text.matchAll(sectionPattern)];
    melData.sections = [...new Set(sections.map(match => match[1]))];
    
    // Extract document metadata
    const revisionMatch = text.match(/(?:revision|rev\.?)\s*(\d+)/i);
    if (revisionMatch) melData.revision = revisionMatch[1];
    
    const dateMatch = text.match(/(?:effective|date)[\s:]+(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/i);
    if (dateMatch) melData.effectiveDate = dateMatch[1];
    
    return melData;
  }

  async cleanupTempFiles(filePaths) {
    try {
      for (const filePath of filePaths) {
        await fs.unlink(filePath);
        // Also clean up enhanced versions
        const enhancedPath = filePath.replace(/\.(png|jpg|jpeg)$/i, '_enhanced.png');
        try {
          await fs.unlink(enhancedPath);
        } catch (e) {
          // Enhanced file might not exist, ignore
        }
      }
    } catch (error) {
      console.error('⚠️ Warning: Failed to cleanup temp files:', error);
    }
  }

  async destroy() {
    if (this.tesseractWorker) {
      await this.tesseractWorker.terminate();
      console.log('🔄 OCR Worker terminated');
    }
  }
}

export default OCRService;
