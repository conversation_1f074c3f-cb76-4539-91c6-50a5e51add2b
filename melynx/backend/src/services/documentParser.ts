import pdfParse from 'pdf-parse'
import mammoth from 'mammoth'
import xml2js from 'xml2js'
import { MELClause, DocumentMetadata } from '../../../shared/src/types/index.js'
import { logger } from '../utils/logger.js'

/**
 * MEL Document Parser Service
 * Extracts MEL clauses from various document formats (PDF, DOCX, XML, JSON)
 */

export interface ParsedDocument {
  metadata: Partial<DocumentMetadata>
  clauses: MELClause[]
  rawText: string
  pageCount?: number
}

export interface ParsingOptions {
  extractMetadata?: boolean
  validateClauses?: boolean
  normalizeContent?: boolean
}

/**
 * Main document parsing function
 * Routes to appropriate parser based on MIME type
 */
export async function parseDocument(
  buffer: Buffer,
  mimeType: string,
  filename: string,
  options: ParsingOptions = {}
): Promise<ParsedDocument> {
  const startTime = Date.now()
  
  logger.info(`Starting document parsing for ${filename} (${mimeType})`)
  
  try {
    let result: ParsedDocument
    
    switch (mimeType) {
      case 'application/pdf':
        result = await parsePDF(buffer, options)
        break
      
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'application/msword':
        result = await parseDOCX(buffer, options)
        break
      
      case 'application/xml':
      case 'text/xml':
        result = await parseXML(buffer, options)
        break
      
      case 'application/json':
        result = await parseJSON(buffer, options)
        break
      
      case 'text/plain':
        result = await parseText(buffer, options)
        break
      
      default:
        throw new Error(`Unsupported file type: ${mimeType}`)
    }
    
    // Post-processing
    if (options.validateClauses) {
      result.clauses = validateClauses(result.clauses)
    }
    
    if (options.normalizeContent) {
      result.clauses = normalizeClauses(result.clauses)
    }
    
    const processingTime = Date.now() - startTime
    logger.info(`Document parsing completed for ${filename} in ${processingTime}ms. Found ${result.clauses.length} clauses.`)
    
    return result
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`Document parsing failed for ${filename} after ${processingTime}ms:`, error)
    throw new Error(`Failed to parse document: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Parse PDF documents using pdf-parse
 */
async function parsePDF(buffer: Buffer, options: ParsingOptions): Promise<ParsedDocument> {
  try {
    const pdfData = await pdfParse(buffer)
    const rawText = pdfData.text
    
    logger.debug(`PDF parsed: ${pdfData.numpages} pages, ${rawText.length} characters`)
    
    const metadata = extractMetadataFromText(rawText)
    const clauses = extractClausesFromText(rawText, 'pdf')
    
    return {
      metadata,
      clauses,
      rawText,
      pageCount: pdfData.numpages
    }
  } catch (error) {
    logger.error('PDF parsing error:', error)
    throw new Error(`PDF parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Parse DOCX documents using mammoth
 */
async function parseDOCX(buffer: Buffer, options: ParsingOptions): Promise<ParsedDocument> {
  try {
    const result = await mammoth.extractRawText({ buffer })
    const rawText = result.value
    
    if (result.messages.length > 0) {
      logger.warn('DOCX parsing warnings:', result.messages)
    }
    
    logger.debug(`DOCX parsed: ${rawText.length} characters`)
    
    const metadata = extractMetadataFromText(rawText)
    const clauses = extractClausesFromText(rawText, 'docx')
    
    return {
      metadata,
      clauses,
      rawText
    }
  } catch (error) {
    logger.error('DOCX parsing error:', error)
    throw new Error(`DOCX parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Parse XML documents
 */
async function parseXML(buffer: Buffer, options: ParsingOptions): Promise<ParsedDocument> {
  try {
    const xmlText = buffer.toString('utf-8')
    const parser = new xml2js.Parser({ explicitArray: false, ignoreAttrs: false })
    const xmlData = await parser.parseStringPromise(xmlText)
    
    logger.debug(`XML parsed successfully`)
    
    // Extract clauses from structured XML
    const clauses = extractClausesFromXML(xmlData)
    const metadata = extractMetadataFromXML(xmlData)
    
    return {
      metadata,
      clauses,
      rawText: xmlText
    }
  } catch (error) {
    logger.error('XML parsing error:', error)
    throw new Error(`XML parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Parse JSON documents (structured MEL data)
 */
async function parseJSON(buffer: Buffer, options: ParsingOptions): Promise<ParsedDocument> {
  try {
    const jsonText = buffer.toString('utf-8')
    const jsonData = JSON.parse(jsonText)
    
    logger.debug(`JSON parsed successfully`)
    
    // Assume JSON follows our MEL structure
    const clauses = extractClausesFromJSON(jsonData)
    const metadata = jsonData.metadata || {}
    
    return {
      metadata,
      clauses,
      rawText: jsonText
    }
  } catch (error) {
    logger.error('JSON parsing error:', error)
    throw new Error(`JSON parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Parse plain text documents
 */
async function parseText(buffer: Buffer, options: ParsingOptions): Promise<ParsedDocument> {
  try {
    const rawText = buffer.toString('utf-8')

    logger.debug(`Text parsed: ${rawText.length} characters`)

    const metadata = extractMetadataFromText(rawText)
    const clauses = extractClausesFromText(rawText, 'text')

    return {
      metadata,
      clauses,
      rawText
    }
  } catch (error) {
    logger.error('Text parsing error:', error)
    throw new Error(`Text parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Extract MEL clauses from plain text using pattern matching
 * Looks for ATA chapter patterns and clause structures
 */
function extractClausesFromText(text: string, sourceFormat: string): MELClause[] {
  const clauses: MELClause[] = []

  // Common MEL clause patterns
  const patterns = {
    // ATA chapter pattern: 27-10, 32-41, etc.
    ataChapter: /(\d{2}-\d{2}(?:-\d{2})?)/g,
    // Clause number pattern: 27-10-01, 32-41-02, etc.
    clauseNumber: /(\d{2}-\d{2}-\d{2}(?:-\d{2})?)/g,
    // Category pattern: (A), (B), (C), (D)
    category: /\(([ABCD])\)/g,
    // Repair interval pattern: (M) (O) etc.
    repairInterval: /\(([MO])\)/g
  }

  // Split text into potential clause sections
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0)

  let currentClause: Partial<MELClause> | null = null
  let clauseContent: string[] = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]

    // Check if line starts a new clause (contains ATA chapter)
    const clauseMatch = line.match(/^(\d{2}-\d{2}-\d{2}(?:-\d{2})?)\s+(.+)/)

    if (clauseMatch) {
      // Save previous clause if exists
      if (currentClause && currentClause.clauseNumber) {
        clauses.push(finalizeClause(currentClause, clauseContent.join(' ')))
      }

      // Start new clause
      const [, clauseNumber, title] = clauseMatch
      currentClause = {
        id: `clause_${clauseNumber}_${Date.now()}`,
        clauseNumber,
        title: title.trim(),
        category: extractCategory(line),
        section: extractATAChapter(clauseNumber)
      }
      clauseContent = []
    } else if (currentClause) {
      // Add content to current clause
      clauseContent.push(line)
    }
  }

  // Don't forget the last clause
  if (currentClause && currentClause.clauseNumber) {
    clauses.push(finalizeClause(currentClause, clauseContent.join(' ')))
  }

  logger.debug(`Extracted ${clauses.length} clauses from ${sourceFormat} text`)
  return clauses
}

/**
 * Extract metadata from document text
 */
function extractMetadataFromText(text: string): Partial<DocumentMetadata> {
  const metadata: Partial<DocumentMetadata> = {}

  // Look for aircraft type
  const aircraftMatch = text.match(/(?:aircraft|a\/c|airplane)[\s:]+([A-Z0-9\-]+)/i)
  if (aircraftMatch) {
    metadata.aircraftType = aircraftMatch[1].trim()
  }

  // Look for operator
  const operatorMatch = text.match(/(?:operator|airline|carrier)[\s:]+([A-Z\s]+)/i)
  if (operatorMatch) {
    metadata.operator = operatorMatch[1].trim()
  }

  // Look for effective date
  const dateMatch = text.match(/(?:effective|date|revision)[\s:]+(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i)
  if (dateMatch) {
    metadata.effectiveDate = dateMatch[1]
  }

  // Look for revision number
  const revisionMatch = text.match(/(?:revision|rev|version)[\s:]+([A-Z0-9\.\-]+)/i)
  if (revisionMatch) {
    metadata.revisionNumber = revisionMatch[1].trim()
  }

  // Determine document type based on content
  if (text.toLowerCase().includes('master mel') || text.toLowerCase().includes('mmel')) {
    metadata.documentType = 'master_mel'
  } else if (text.toLowerCase().includes('operator mel') || text.toLowerCase().includes('omel')) {
    metadata.documentType = 'operator_mel'
  }

  return metadata
}

/**
 * Extract clauses from structured XML data
 */
function extractClausesFromXML(xmlData: any): MELClause[] {
  const clauses: MELClause[] = []

  try {
    // Handle different XML structures
    const melData = xmlData.MEL || xmlData.mel || xmlData.root || xmlData

    if (melData.clauses && Array.isArray(melData.clauses)) {
      melData.clauses.forEach((clauseData: any, index: number) => {
        const clause = createClauseFromXMLData(clauseData, index)
        if (clause) clauses.push(clause)
      })
    } else if (melData.clause) {
      // Single clause or array of clauses
      const clauseArray = Array.isArray(melData.clause) ? melData.clause : [melData.clause]
      clauseArray.forEach((clauseData: any, index: number) => {
        const clause = createClauseFromXMLData(clauseData, index)
        if (clause) clauses.push(clause)
      })
    }

    logger.debug(`Extracted ${clauses.length} clauses from XML structure`)
  } catch (error) {
    logger.error('Error extracting clauses from XML:', error)
  }

  return clauses
}

/**
 * Extract clauses from JSON data
 */
function extractClausesFromJSON(jsonData: any): MELClause[] {
  const clauses: MELClause[] = []

  try {
    if (jsonData.clauses && Array.isArray(jsonData.clauses)) {
      jsonData.clauses.forEach((clauseData: any) => {
        const clause = createClauseFromJSONData(clauseData)
        if (clause) clauses.push(clause)
      })
    }

    logger.debug(`Extracted ${clauses.length} clauses from JSON structure`)
  } catch (error) {
    logger.error('Error extracting clauses from JSON:', error)
  }

  return clauses
}

/**
 * Helper function to create MEL clause from XML data
 */
function createClauseFromXMLData(clauseData: any, index: number): MELClause | null {
  try {
    const clause: MELClause = {
      id: clauseData.$.id || `xml_clause_${index}_${Date.now()}`,
      clauseNumber: clauseData.number || clauseData.$.number || `unknown-${index}`,
      title: clauseData.title || clauseData.$.title || 'Untitled Clause',
      content: clauseData.content || clauseData.description || '',
      category: clauseData.category || clauseData.$.category || 'C',
      subcategory: clauseData.subcategory,
      conditions: parseArrayField(clauseData.conditions),
      limitations: parseArrayField(clauseData.limitations),
      maintenanceActions: parseArrayField(clauseData.maintenanceActions),
      operationalProcedures: parseArrayField(clauseData.operationalProcedures),
      placard: clauseData.placard,
      remarks: clauseData.remarks,
      page: clauseData.page ? parseInt(clauseData.page) : undefined,
      section: clauseData.section || extractATAChapter(clauseData.number || '')
    }

    return clause
  } catch (error) {
    logger.error(`Error creating clause from XML data at index ${index}:`, error)
    return null
  }
}

/**
 * Helper function to create MEL clause from JSON data
 */
function createClauseFromJSONData(clauseData: any): MELClause | null {
  try {
    const clause: MELClause = {
      id: clauseData.id || `json_clause_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      clauseNumber: clauseData.clauseNumber || clauseData.number || 'unknown',
      title: clauseData.title || 'Untitled Clause',
      content: clauseData.content || clauseData.description || '',
      category: clauseData.category || 'C',
      subcategory: clauseData.subcategory,
      conditions: clauseData.conditions || [],
      limitations: clauseData.limitations || [],
      maintenanceActions: clauseData.maintenanceActions || [],
      operationalProcedures: clauseData.operationalProcedures || [],
      placard: clauseData.placard,
      remarks: clauseData.remarks,
      page: clauseData.page,
      section: clauseData.section || extractATAChapter(clauseData.clauseNumber || '')
    }

    return clause
  } catch (error) {
    logger.error('Error creating clause from JSON data:', error)
    return null
  }
}

/**
 * Finalize clause with content and extract additional information
 */
function finalizeClause(partialClause: Partial<MELClause>, content: string): MELClause {
  const clause: MELClause = {
    id: partialClause.id || `clause_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    clauseNumber: partialClause.clauseNumber || 'unknown',
    title: partialClause.title || 'Untitled Clause',
    content: content.trim(),
    category: partialClause.category || extractCategory(content) || 'C',
    subcategory: partialClause.subcategory,
    section: partialClause.section || extractATAChapter(partialClause.clauseNumber || ''),
    page: partialClause.page
  }

  // Extract additional information from content
  const extracted = extractClauseDetails(content)
  clause.conditions = extracted.conditions
  clause.limitations = extracted.limitations
  clause.maintenanceActions = extracted.maintenanceActions
  clause.operationalProcedures = extracted.operationalProcedures
  clause.placard = extracted.placard
  clause.remarks = extracted.remarks

  return clause
}

/**
 * Extract category (A, B, C, D) from text
 */
function extractCategory(text: string): string | undefined {
  const categoryMatch = text.match(/\(([ABCD])\)/)
  return categoryMatch ? categoryMatch[1] : undefined
}

/**
 * Extract ATA chapter from clause number
 */
function extractATAChapter(clauseNumber: string): string {
  const match = clauseNumber.match(/^(\d{2}-\d{2})/)
  return match ? match[1] : 'unknown'
}

/**
 * Extract detailed information from clause content
 */
function extractClauseDetails(content: string): {
  conditions?: string[]
  limitations?: string[]
  maintenanceActions?: string[]
  operationalProcedures?: string[]
  placard?: string
  remarks?: string
} {
  const details: any = {}

  // Extract conditions (lines starting with "Conditions:")
  const conditionsMatch = content.match(/(?:conditions?|requirements?):\s*([^\n]+(?:\n[^\n]+)*)/i)
  if (conditionsMatch) {
    details.conditions = conditionsMatch[1].split(/[,;]/).map(c => c.trim()).filter(c => c.length > 0)
  }

  // Extract limitations (lines starting with "Limitations:")
  const limitationsMatch = content.match(/limitations?:\s*([^\n]+(?:\n[^\n]+)*)/i)
  if (limitationsMatch) {
    details.limitations = limitationsMatch[1].split(/[,;]/).map(l => l.trim()).filter(l => l.length > 0)
  }

  // Extract maintenance actions
  const maintenanceMatch = content.match(/(?:maintenance|repair|action):\s*([^\n]+(?:\n[^\n]+)*)/i)
  if (maintenanceMatch) {
    details.maintenanceActions = maintenanceMatch[1].split(/[,;]/).map(m => m.trim()).filter(m => m.length > 0)
  }

  // Extract operational procedures
  const proceduresMatch = content.match(/(?:procedures?|operations?):\s*([^\n]+(?:\n[^\n]+)*)/i)
  if (proceduresMatch) {
    details.operationalProcedures = proceduresMatch[1].split(/[,;]/).map(p => p.trim()).filter(p => p.length > 0)
  }

  // Extract placard information
  const placardMatch = content.match(/placard:\s*([^\n]+)/i)
  if (placardMatch) {
    details.placard = placardMatch[1].trim()
  }

  // Extract remarks
  const remarksMatch = content.match(/(?:remarks?|notes?):\s*([^\n]+(?:\n[^\n]+)*)/i)
  if (remarksMatch) {
    details.remarks = remarksMatch[1].trim()
  }

  return details
}

/**
 * Parse array field from XML/JSON (handles both string and array inputs)
 */
function parseArrayField(field: any): string[] | undefined {
  if (!field) return undefined

  if (Array.isArray(field)) {
    return field.map(item => String(item).trim()).filter(item => item.length > 0)
  }

  if (typeof field === 'string') {
    return field.split(/[,;]/).map(item => item.trim()).filter(item => item.length > 0)
  }

  return undefined
}

/**
 * Extract metadata from XML structure
 */
function extractMetadataFromXML(xmlData: any): Partial<DocumentMetadata> {
  const metadata: Partial<DocumentMetadata> = {}

  try {
    const melData = xmlData.MEL || xmlData.mel || xmlData.root || xmlData

    if (melData.metadata) {
      const meta = melData.metadata
      metadata.aircraftType = meta.aircraftType || meta.aircraft
      metadata.operator = meta.operator || meta.airline
      metadata.effectiveDate = meta.effectiveDate || meta.date
      metadata.revisionNumber = meta.revisionNumber || meta.revision
      metadata.documentType = meta.documentType || (meta.type === 'MMEL' ? 'master_mel' : 'operator_mel')
      metadata.language = meta.language
    }

    // Try to extract from attributes
    if (melData.$ && melData.$.type) {
      metadata.documentType = melData.$.type === 'MMEL' ? 'master_mel' : 'operator_mel'
    }
  } catch (error) {
    logger.error('Error extracting metadata from XML:', error)
  }

  return metadata
}

/**
 * Validate extracted clauses for completeness and correctness
 */
function validateClauses(clauses: MELClause[]): MELClause[] {
  const validatedClauses: MELClause[] = []

  for (const clause of clauses) {
    // Check required fields
    if (!clause.clauseNumber || !clause.title) {
      logger.warn(`Skipping invalid clause: missing required fields`, {
        clauseNumber: clause.clauseNumber,
        title: clause.title
      })
      continue
    }

    // Validate clause number format (should be ATA format)
    if (!clause.clauseNumber.match(/^\d{2}-\d{2}(-\d{2})?(-\d{2})?$/)) {
      logger.warn(`Invalid clause number format: ${clause.clauseNumber}`)
      // Try to fix common issues
      clause.clauseNumber = normalizeClauseNumber(clause.clauseNumber)
    }

    // Validate category
    if (clause.category && !['A', 'B', 'C', 'D'].includes(clause.category)) {
      logger.warn(`Invalid category: ${clause.category}, defaulting to C`)
      clause.category = 'C'
    }

    validatedClauses.push(clause)
  }

  logger.info(`Validated ${validatedClauses.length} out of ${clauses.length} clauses`)
  return validatedClauses
}

/**
 * Normalize clause content and structure
 */
function normalizeClauses(clauses: MELClause[]): MELClause[] {
  return clauses.map(clause => {
    // Normalize text content
    clause.content = normalizeText(clause.content)
    clause.title = normalizeText(clause.title)

    // Normalize arrays
    if (clause.conditions) {
      clause.conditions = clause.conditions.map(normalizeText).filter(c => c.length > 0)
    }
    if (clause.limitations) {
      clause.limitations = clause.limitations.map(normalizeText).filter(l => l.length > 0)
    }
    if (clause.maintenanceActions) {
      clause.maintenanceActions = clause.maintenanceActions.map(normalizeText).filter(m => m.length > 0)
    }
    if (clause.operationalProcedures) {
      clause.operationalProcedures = clause.operationalProcedures.map(normalizeText).filter(p => p.length > 0)
    }

    // Ensure section is set
    if (!clause.section) {
      clause.section = extractATAChapter(clause.clauseNumber)
    }

    return clause
  })
}

/**
 * Normalize text content (remove extra whitespace, fix encoding issues)
 */
function normalizeText(text: string): string {
  if (!text) return ''

  return text
    .replace(/\s+/g, ' ')  // Replace multiple whitespace with single space
    .replace(/[""]/g, '"') // Normalize quotes
    .replace(/['']/g, "'") // Normalize apostrophes
    .trim()
}

/**
 * Normalize clause number to standard ATA format
 */
function normalizeClauseNumber(clauseNumber: string): string {
  // Remove any non-digit, non-dash characters
  let normalized = clauseNumber.replace(/[^\d-]/g, '')

  // Try to match common patterns and fix them
  const patterns = [
    /^(\d{2})(\d{2})(\d{2})$/, // 271001 -> 27-10-01
    /^(\d{2})-(\d{2})(\d{2})$/, // 27-1001 -> 27-10-01
    /^(\d{2})(\d{2})-(\d{2})$/, // 2710-01 -> 27-10-01
  ]

  for (const pattern of patterns) {
    const match = normalized.match(pattern)
    if (match) {
      return `${match[1]}-${match[2]}-${match[3]}`
    }
  }

  // If no pattern matches, return as-is
  return normalized
}
