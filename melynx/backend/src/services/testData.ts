/**
 * Test data and examples for MEL parsing functionality
 * This file contains sample MEL content for testing the document parser
 */

export const SAMPLE_MEL_TEXT = `
BOEING 737-800 MASTER MINIMUM EQUIPMENT LIST (MMEL)
Revision 15
Effective Date: January 15, 2024
Operator: Sample Airlines

ATA CHAPTER 27 - FLIGHT CONTROLS

27-10-01 Aileron Tab (C)
(M) May be inoperative provided:
a) Flight is conducted in accordance with approved procedures
b) Maintenance action is accomplished within 10 flight hours
Conditions: VFR flight only
Limitations: Maximum crosswind 15 knots
Placard: "AILERON TAB INOP"

27-10-02 Elevator Tab (B)
(O) May be inoperative provided:
a) Elevator control forces are within normal limits
b) Flight crew is briefed on handling characteristics
Maintenance: Repair within 3 flight cycles
Procedures: Use alternate elevator control technique

27-21-01 Rudder Trim Tab (C)
(M) May be inoperative provided:
a) Rudder forces can be controlled manually
b) Weather conditions are suitable for manual control
Limitations: No autoland approaches
Remarks: Consider weight and balance implications

ATA CHAPTER 32 - LANDING GEAR

32-41-01 Main Landing Gear Door (A)
(O) May be inoperative provided:
a) Door is secured in closed position
b) No hydraulic leakage is evident
c) Landing gear operation is verified normal
Conditions: Day VFR only
Maintenance: Repair before next flight

32-41-02 Nose Landing Gear Door (B)
(M) May be inoperative provided:
a) Door does not interfere with gear operation
b) Aerodynamic effects are acceptable
Limitations: Maximum speed 250 knots
Placard: "NLG DOOR INOP - MAX 250 KTS"

32-42-01 Landing Gear Position Indicator (C)
(O) May be inoperative provided:
a) Alternate means of gear position verification available
b) Visual inspection confirms gear position
c) Flight crew trained on alternate procedures
Procedures: Use backup indication system
Remarks: Coordinate with maintenance control
`;

export const SAMPLE_XML_MEL = `<?xml version="1.0" encoding="UTF-8"?>
<MEL type="MMEL" aircraft="Boeing 737-800" revision="15">
  <metadata>
    <aircraftType>Boeing 737-800</aircraftType>
    <operator>Sample Airlines</operator>
    <effectiveDate>2024-01-15</effectiveDate>
    <revisionNumber>15</revisionNumber>
    <documentType>master_mel</documentType>
    <language>en</language>
  </metadata>
  
  <clauses>
    <clause number="27-10-01" category="C" section="27-10">
      <title>Aileron Tab</title>
      <content>May be inoperative provided flight is conducted in accordance with approved procedures</content>
      <conditions>
        <condition>VFR flight only</condition>
        <condition>Flight crew briefed</condition>
      </conditions>
      <limitations>
        <limitation>Maximum crosswind 15 knots</limitation>
      </limitations>
      <maintenanceActions>
        <action>Repair within 10 flight hours</action>
      </maintenanceActions>
      <placard>AILERON TAB INOP</placard>
    </clause>
    
    <clause number="32-41-01" category="A" section="32-41">
      <title>Main Landing Gear Door</title>
      <content>May be inoperative provided door is secured and no leakage evident</content>
      <conditions>
        <condition>Day VFR only</condition>
        <condition>Door secured in closed position</condition>
      </conditions>
      <maintenanceActions>
        <action>Repair before next flight</action>
      </maintenanceActions>
    </clause>
  </clauses>
</MEL>`;

export const SAMPLE_JSON_MEL = {
  metadata: {
    aircraftType: "Boeing 737-800",
    operator: "Sample Airlines",
    effectiveDate: "2024-01-15",
    revisionNumber: "15",
    documentType: "master_mel",
    language: "en"
  },
  clauses: [
    {
      id: "clause_27_10_01",
      clauseNumber: "27-10-01",
      title: "Aileron Tab",
      content: "May be inoperative provided flight is conducted in accordance with approved procedures",
      category: "C",
      section: "27-10",
      conditions: ["VFR flight only", "Flight crew briefed"],
      limitations: ["Maximum crosswind 15 knots"],
      maintenanceActions: ["Repair within 10 flight hours"],
      placard: "AILERON TAB INOP"
    },
    {
      id: "clause_32_41_01",
      clauseNumber: "32-41-01",
      title: "Main Landing Gear Door",
      content: "May be inoperative provided door is secured and no leakage evident",
      category: "A",
      section: "32-41",
      conditions: ["Day VFR only", "Door secured in closed position"],
      maintenanceActions: ["Repair before next flight"]
    }
  ]
};

/**
 * Sample MEL clause extraction patterns for testing
 */
export const MEL_PATTERNS = {
  // Common ATA chapter patterns
  ataChapters: [
    "21", "22", "23", "24", "25", "26", "27", "28", "29", "30",
    "31", "32", "33", "34", "35", "36", "49", "52", "53", "54",
    "55", "56", "57", "71", "72", "73", "74", "75", "76", "77",
    "78", "79", "80", "81", "82", "83", "84", "85", "91", "92"
  ],
  
  // Sample clause numbers
  clauseNumbers: [
    "27-10-01", "27-10-02", "27-21-01",
    "32-41-01", "32-41-02", "32-42-01",
    "34-11-01", "34-12-01", "34-13-01"
  ],
  
  // Categories
  categories: ["A", "B", "C", "D"],
  
  // Common MEL keywords
  keywords: [
    "may be inoperative",
    "provided",
    "conditions",
    "limitations",
    "maintenance",
    "procedures",
    "placard",
    "remarks",
    "repair interval",
    "flight hours",
    "flight cycles"
  ]
};

/**
 * Expected parsing results for validation
 */
export const EXPECTED_PARSING_RESULTS = {
  textParsing: {
    clauseCount: 6,
    sections: ["27-10", "27-21", "32-41", "32-42"],
    categories: ["A", "B", "C"],
    firstClause: {
      clauseNumber: "27-10-01",
      title: "Aileron Tab",
      category: "C",
      section: "27-10"
    }
  },
  
  xmlParsing: {
    clauseCount: 2,
    sections: ["27-10", "32-41"],
    categories: ["A", "C"],
    metadata: {
      aircraftType: "Boeing 737-800",
      documentType: "master_mel"
    }
  },
  
  jsonParsing: {
    clauseCount: 2,
    sections: ["27-10", "32-41"],
    categories: ["A", "C"],
    metadata: {
      aircraftType: "Boeing 737-800",
      documentType: "master_mel"
    }
  }
};

/**
 * Create test buffers for different file types
 */
export function createTestBuffers() {
  return {
    text: Buffer.from(SAMPLE_MEL_TEXT, 'utf-8'),
    xml: Buffer.from(SAMPLE_XML_MEL, 'utf-8'),
    json: Buffer.from(JSON.stringify(SAMPLE_JSON_MEL, null, 2), 'utf-8')
  };
}

/**
 * Validate parsing results against expected outcomes
 */
export function validateParsingResults(results: any, expected: any): boolean {
  try {
    if (results.clauses.length !== expected.clauseCount) {
      console.error(`Clause count mismatch: expected ${expected.clauseCount}, got ${results.clauses.length}`);
      return false;
    }
    
    const sections = [...new Set(results.clauses.map((c: any) => c.section))];
    if (sections.length !== expected.sections.length) {
      console.error(`Section count mismatch: expected ${expected.sections.length}, got ${sections.length}`);
      return false;
    }
    
    const categories = [...new Set(results.clauses.map((c: any) => c.category))];
    if (categories.length !== expected.categories.length) {
      console.error(`Category count mismatch: expected ${expected.categories.length}, got ${categories.length}`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Validation error:', error);
    return false;
  }
}
