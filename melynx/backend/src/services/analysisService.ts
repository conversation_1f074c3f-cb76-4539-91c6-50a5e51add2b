import { supabase } from '../utils/supabase.js'
import { logger } from '../utils/logger.js'
import { getClauses } from './clauseService.js'
import { getDocument } from './storageService.js'
import { compareMELClauses, ComparisonResults, MELDifference } from './melComparator.js'
import { generateComplianceAnalysis, convertToAnalysisResults, generateQuickSummary, AIAnalysisResponse } from './aiSummarizer.js'
import { 
  MELAnalysis, 
  AnalysisStatus, 
  AnalysisOptions, 
  AnalysisResults,
  DocumentStatus,
  ComplianceIssue,
  IssueSeverity,
  IssueType
} from '@shared/types/index.js'

/**
 * Analysis Service
 * Orchestrates the complete MEL compliance analysis process
 */

export interface StartAnalysisRequest {
  operatorMelId: string
  masterMelId: string
  createdBy: string
  options?: Partial<AnalysisOptions>
}

export interface AnalysisProgress {
  analysisId: string
  status: AnalysisStatus
  progress: number
  currentStage: string
  estimatedTimeRemaining?: number
  error?: string
}

/**
 * Start a new MEL compliance analysis
 */
export async function startAnalysis(request: StartAnalysisRequest): Promise<MELAnalysis> {
  const startTime = Date.now()
  
  try {
    logger.info(`Starting MEL analysis: operator=${request.operatorMelId}, master=${request.masterMelId}`)
    
    // Validate documents exist and are parsed
    const operatorDoc = await getDocument(request.operatorMelId)
    const masterDoc = await getDocument(request.masterMelId)
    
    if (!operatorDoc) {
      throw new Error(`Operator MEL document not found: ${request.operatorMelId}`)
    }
    
    if (!masterDoc) {
      throw new Error(`Master MEL document not found: ${request.masterMelId}`)
    }
    
    if (operatorDoc.status !== DocumentStatus.PARSED) {
      throw new Error(`Operator MEL document not parsed: ${operatorDoc.status}`)
    }
    
    if (masterDoc.status !== DocumentStatus.PARSED) {
      throw new Error(`Master MEL document not parsed: ${masterDoc.status}`)
    }
    
    // Create analysis record in database
    const analysisOptions: AnalysisOptions = {
      clauseByClauseComparison: true,
      complianceGapAnalysis: true,
      generateDetailedReport: true,
      enableRealtimeChat: false,
      includeRecommendations: true,
      ...request.options
    }
    
    const { data: analysisData, error: analysisError } = await supabase
      .from('mel_evaluations')
      .insert({
        operator_mel_id: request.operatorMelId,
        master_mel_id: request.masterMelId,
        created_by: request.createdBy,
        status: AnalysisStatus.INITIATED,
        clause_by_clause_comparison: analysisOptions.clauseByClauseComparison,
        compliance_gap_analysis: analysisOptions.complianceGapAnalysis,
        generate_detailed_report: analysisOptions.generateDetailedReport,
        enable_realtime_chat: analysisOptions.enableRealtimeChat,
        include_recommendations: analysisOptions.includeRecommendations
      })
      .select()
      .single()
    
    if (analysisError) {
      throw new Error(`Failed to create analysis record: ${analysisError.message}`)
    }
    
    const analysis: MELAnalysis = {
      id: analysisData.id,
      operatorMelId: request.operatorMelId,
      masterMelId: request.masterMelId,
      status: AnalysisStatus.INITIATED,
      startedAt: analysisData.started_at,
      options: analysisOptions,
      createdBy: request.createdBy
    }
    
    // Start background processing
    processAnalysisAsync(analysis.id, operatorDoc, masterDoc, analysisOptions)
      .catch(error => {
        logger.error(`Background analysis processing failed for ${analysis.id}:`, error)
      })
    
    const processingTime = Date.now() - startTime
    logger.info(`Analysis initiated in ${processingTime}ms: ${analysis.id}`)
    
    return analysis
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`Failed to start analysis after ${processingTime}ms:`, error)
    throw error
  }
}

/**
 * Get analysis status and progress
 */
export async function getAnalysisStatus(analysisId: string): Promise<AnalysisProgress> {
  try {
    const { data, error } = await supabase
      .from('mel_evaluations')
      .select('*')
      .eq('id', analysisId)
      .single()
    
    if (error) {
      throw new Error(`Failed to get analysis status: ${error.message}`)
    }
    
    if (!data) {
      throw new Error(`Analysis not found: ${analysisId}`)
    }
    
    // Calculate progress based on status
    const progressMap: Record<AnalysisStatus, number> = {
      [AnalysisStatus.INITIATED]: 5,
      [AnalysisStatus.PARSING_DOCUMENTS]: 15,
      [AnalysisStatus.EXTRACTING_CLAUSES]: 25,
      [AnalysisStatus.COMPARING_CLAUSES]: 50,
      [AnalysisStatus.GENERATING_SUMMARY]: 80,
      [AnalysisStatus.COMPLETED]: 100,
      [AnalysisStatus.FAILED]: 0,
      [AnalysisStatus.CANCELLED]: 0
    }
    
    const stageMap: Record<AnalysisStatus, string> = {
      [AnalysisStatus.INITIATED]: 'Initializing analysis',
      [AnalysisStatus.PARSING_DOCUMENTS]: 'Loading document data',
      [AnalysisStatus.EXTRACTING_CLAUSES]: 'Extracting MEL clauses',
      [AnalysisStatus.COMPARING_CLAUSES]: 'Comparing clauses',
      [AnalysisStatus.GENERATING_SUMMARY]: 'Generating AI summary',
      [AnalysisStatus.COMPLETED]: 'Analysis complete',
      [AnalysisStatus.FAILED]: 'Analysis failed',
      [AnalysisStatus.CANCELLED]: 'Analysis cancelled'
    }
    
    return {
      analysisId,
      status: data.status as AnalysisStatus,
      progress: progressMap[data.status as AnalysisStatus] || 0,
      currentStage: stageMap[data.status as AnalysisStatus] || 'Unknown stage'
    }
    
  } catch (error) {
    logger.error(`Failed to get analysis status for ${analysisId}:`, error)
    throw error
  }
}

/**
 * Get completed analysis results
 */
export async function getAnalysisResults(analysisId: string): Promise<AnalysisResults> {
  try {
    const { data, error } = await supabase
      .from('mel_evaluations')
      .select('*')
      .eq('id', analysisId)
      .single()
    
    if (error) {
      throw new Error(`Failed to get analysis results: ${error.message}`)
    }
    
    if (!data) {
      throw new Error(`Analysis not found: ${analysisId}`)
    }
    
    if (data.status !== AnalysisStatus.COMPLETED) {
      throw new Error(`Analysis not completed: ${data.status}`)
    }
    
    if (!data.results) {
      throw new Error('Analysis results not available')
    }
    
    return data.results as AnalysisResults
    
  } catch (error) {
    logger.error(`Failed to get analysis results for ${analysisId}:`, error)
    throw error
  }
}

/**
 * Cancel an ongoing analysis
 */
export async function cancelAnalysis(analysisId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('mel_evaluations')
      .update({ 
        status: AnalysisStatus.CANCELLED,
        updated_at: new Date().toISOString()
      })
      .eq('id', analysisId)
    
    if (error) {
      throw new Error(`Failed to cancel analysis: ${error.message}`)
    }
    
    logger.info(`Analysis cancelled: ${analysisId}`)
    
  } catch (error) {
    logger.error(`Failed to cancel analysis ${analysisId}:`, error)
    throw error
  }
}

/**
 * Background processing function for analysis
 */
async function processAnalysisAsync(
  analysisId: string,
  operatorDoc: any,
  masterDoc: any,
  options: AnalysisOptions
): Promise<void> {
  try {
    logger.info(`Starting background processing for analysis ${analysisId}`)
    
    // Update status to extracting clauses
    await updateAnalysisStatus(analysisId, AnalysisStatus.EXTRACTING_CLAUSES)
    
    // Get clauses for both documents
    const operatorClauses = await getClauses(operatorDoc.id)
    const masterClauses = await getClauses(masterDoc.id)
    
    logger.info(`Loaded clauses: ${operatorClauses.length} operator, ${masterClauses.length} master`)
    
    // Update status to comparing clauses
    await updateAnalysisStatus(analysisId, AnalysisStatus.COMPARING_CLAUSES)
    
    // Perform clause comparison
    const comparisonResults = await compareMELClauses(operatorClauses, masterClauses, {
      strictCategoryMatching: true,
      checkMaintenanceActions: options.clauseByClauseComparison,
      checkOperationalProcedures: options.clauseByClauseComparison,
      checkConditionsAndLimitations: options.clauseByClauseComparison
    })
    
    // Save differences to database
    await saveDifferencesToDatabase(analysisId, comparisonResults.differences)
    
    // Update status to generating summary
    await updateAnalysisStatus(analysisId, AnalysisStatus.GENERATING_SUMMARY)
    
    let aiAnalysis: AIAnalysisResponse | null = null
    
    if (options.includeRecommendations) {
      try {
        // Generate AI analysis
        aiAnalysis = await generateComplianceAnalysis({
          differences: comparisonResults.differences,
          comparisonResults,
          aircraftType: operatorDoc.metadata.aircraftType || 'Unknown',
          operatorName: operatorDoc.metadata.operator,
          analysisOptions: {
            includeRecommendations: options.includeRecommendations,
            focusOnCriticalIssues: true,
            includeRegulatoryReferences: true
          }
        })
        
        logger.info(`AI analysis completed with ${aiAnalysis.complianceAssessment} assessment`)
        
      } catch (aiError) {
        logger.error(`AI analysis failed for ${analysisId}:`, aiError)
        // Continue without AI analysis
      }
    }
    
    // Prepare final results
    const analysisResults: AnalysisResults = aiAnalysis 
      ? convertToAnalysisResults(comparisonResults, aiAnalysis)
      : {
          complianceRate: comparisonResults.complianceRate,
          totalClauses: comparisonResults.totalMasterClauses,
          matchedClauses: comparisonResults.matchedClauses,
          missingClauses: comparisonResults.missingClauses,
          modifiedClauses: comparisonResults.modifiedClauses,
          additionalClauses: comparisonResults.additionalClauses,
          criticalIssues: [],
          warnings: [],
          recommendations: ['Manual review recommended'],
          summary: `Analysis completed. Compliance rate: ${comparisonResults.complianceRate}%`,
          clauseComparisons: comparisonResults.clauseComparisons
        }
    
    // Save results and mark as completed
    await completeAnalysis(analysisId, analysisResults)
    
    logger.info(`Analysis completed successfully: ${analysisId}`)
    
  } catch (error) {
    logger.error(`Analysis processing failed for ${analysisId}:`, error)
    await updateAnalysisStatus(analysisId, AnalysisStatus.FAILED)
  }
}

/**
 * Update analysis status in database
 */
async function updateAnalysisStatus(analysisId: string, status: AnalysisStatus): Promise<void> {
  try {
    const { error } = await supabase
      .from('mel_evaluations')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', analysisId)

    if (error) {
      throw new Error(`Failed to update analysis status: ${error.message}`)
    }

    logger.debug(`Analysis status updated: ${analysisId} -> ${status}`)

  } catch (error) {
    logger.error(`Failed to update analysis status for ${analysisId}:`, error)
    throw error
  }
}

/**
 * Save differences to database
 */
async function saveDifferencesToDatabase(analysisId: string, differences: MELDifference[]): Promise<void> {
  try {
    if (differences.length === 0) {
      logger.debug(`No differences to save for analysis ${analysisId}`)
      return
    }

    const differenceData = differences.map(diff => ({
      evaluation_id: analysisId,
      operator_clause_id: diff.operatorClause?.id || null,
      master_clause_id: diff.mmelClause?.id || null,
      comparison_status: diff.differenceType,
      issue_type: mapDifferenceTypeToIssueType(diff.differenceType),
      severity: diff.severity,
      title: `${diff.clauseNumber} - ${diff.differenceType}`,
      description: diff.differences.join('; '),
      differences: diff.differences,
      recommendation: diff.recommendation,
      regulatory_reference: null
    }))

    // Insert in batches to avoid query size limits
    const batchSize = 100
    for (let i = 0; i < differenceData.length; i += batchSize) {
      const batch = differenceData.slice(i, i + batchSize)

      const { error } = await supabase
        .from('mel_differences')
        .insert(batch)

      if (error) {
        throw new Error(`Failed to insert differences batch: ${error.message}`)
      }
    }

    logger.info(`Saved ${differences.length} differences for analysis ${analysisId}`)

  } catch (error) {
    logger.error(`Failed to save differences for ${analysisId}:`, error)
    throw error
  }
}

/**
 * Complete analysis and save results
 */
async function completeAnalysis(analysisId: string, results: AnalysisResults): Promise<void> {
  try {
    const { error } = await supabase
      .from('mel_evaluations')
      .update({
        status: AnalysisStatus.COMPLETED,
        results: results,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', analysisId)

    if (error) {
      throw new Error(`Failed to complete analysis: ${error.message}`)
    }

    logger.info(`Analysis completed and results saved: ${analysisId}`)

  } catch (error) {
    logger.error(`Failed to complete analysis ${analysisId}:`, error)
    throw error
  }
}

/**
 * Map difference type to issue type enum
 */
function mapDifferenceTypeToIssueType(differenceType: string): IssueType {
  switch (differenceType) {
    case 'missing':
      return IssueType.MISSING_CLAUSE
    case 'modified':
      return IssueType.MODIFIED_CLAUSE
    case 'additional':
      return IssueType.ADDITIONAL_CLAUSE
    case 'conflicting':
      return IssueType.CONFLICTING_REQUIREMENTS
    default:
      return IssueType.REGULATORY_COMPLIANCE
  }
}

/**
 * List analyses for a user
 */
export async function listAnalyses(
  createdBy?: string,
  status?: AnalysisStatus,
  limit: number = 20,
  offset: number = 0
): Promise<MELAnalysis[]> {
  try {
    let query = supabase
      .from('mel_evaluations')
      .select(`
        *,
        operator_mel:mel_documents!operator_mel_id(original_name, aircraft_type),
        master_mel:mel_documents!master_mel_id(original_name, aircraft_type)
      `)
      .order('started_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (createdBy) {
      query = query.eq('created_by', createdBy)
    }

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to list analyses: ${error.message}`)
    }

    return data.map(item => ({
      id: item.id,
      operatorMelId: item.operator_mel_id,
      masterMelId: item.master_mel_id,
      status: item.status as AnalysisStatus,
      startedAt: item.started_at,
      completedAt: item.completed_at,
      results: item.results as AnalysisResults,
      options: {
        clauseByClauseComparison: item.clause_by_clause_comparison,
        complianceGapAnalysis: item.compliance_gap_analysis,
        generateDetailedReport: item.generate_detailed_report,
        enableRealtimeChat: item.enable_realtime_chat,
        includeRecommendations: item.include_recommendations
      },
      createdBy: item.created_by
    }))

  } catch (error) {
    logger.error('Failed to list analyses:', error)
    throw error
  }
}
