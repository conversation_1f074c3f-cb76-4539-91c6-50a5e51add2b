import { <PERSON><PERSON><PERSON><PERSON>, ClauseComparison, Comp<PERSON>onStatus, IssueSeverity, IssueType } from '@shared/types/index.js'
import { logger } from '../utils/logger.js'

/**
 * MEL Comparator Service
 * Compares Operator MEL clauses against Master MEL (MMEL) clauses
 * Detects missing, modified, additional, and conflicting clauses
 */

export interface MELDifference {
  ataChapter: string
  clauseNumber: string
  differenceType: 'missing' | 'modified' | 'additional' | 'conflicting'
  mmelClause?: MELClause
  operatorClause?: MELClause
  severity: IssueSeverity
  differences: string[]
  recommendation?: string
}

export interface ComparisonOptions {
  strictCategoryMatching?: boolean
  ignoreFormattingDifferences?: boolean
  checkMaintenanceActions?: boolean
  checkOperationalProcedures?: boolean
  checkConditionsAndLimitations?: boolean
}

export interface ComparisonResults {
  totalOperatorClauses: number
  totalMasterClauses: number
  matchedClauses: number
  missingClauses: number
  modifiedClauses: number
  additionalClauses: number
  conflictingClauses: number
  complianceRate: number
  differences: MELDifference[]
  clauseComparisons: ClauseComparison[]
}

/**
 * Main comparison function
 * Compares Operator MEL clauses against Master MEL clauses
 */
export async function compareMELClauses(
  operatorClauses: MELClause[],
  masterClauses: MELClause[],
  options: ComparisonOptions = {}
): Promise<ComparisonResults> {
  const startTime = Date.now()
  
  logger.info(`Starting MEL comparison: ${operatorClauses.length} operator clauses vs ${masterClauses.length} master clauses`)
  
  try {
    // Create lookup maps for efficient comparison
    const masterClauseMap = createClauseMap(masterClauses)
    const operatorClauseMap = createClauseMap(operatorClauses)
    
    const differences: MELDifference[] = []
    const clauseComparisons: ClauseComparison[] = []
    
    let matchedCount = 0
    let missingCount = 0
    let modifiedCount = 0
    let additionalCount = 0
    let conflictingCount = 0
    
    // Check each master clause against operator clauses
    for (const masterClause of masterClauses) {
      const operatorClause = operatorClauseMap.get(masterClause.clauseNumber)
      
      if (!operatorClause) {
        // Missing clause in operator MEL
        const difference = createMissingClauseDifference(masterClause)
        differences.push(difference)
        
        const comparison = createClauseComparison(
          undefined,
          masterClause,
          ComparisonStatus.MISSING,
          ['Clause missing from Operator MEL'],
          IssueSeverity.HIGH
        )
        clauseComparisons.push(comparison)
        missingCount++
        
      } else {
        // Compare existing clauses
        const comparisonResult = compareIndividualClauses(operatorClause, masterClause, options)
        clauseComparisons.push(comparisonResult)
        
        if (comparisonResult.status === ComparisonStatus.MATCHED) {
          matchedCount++
        } else if (comparisonResult.status === ComparisonStatus.MODIFIED) {
          const difference = createModifiedClauseDifference(operatorClause, masterClause, comparisonResult.differences)
          differences.push(difference)
          modifiedCount++
        } else if (comparisonResult.status === ComparisonStatus.CONFLICTING) {
          const difference = createConflictingClauseDifference(operatorClause, masterClause, comparisonResult.differences)
          differences.push(difference)
          conflictingCount++
        }
      }
    }
    
    // Check for additional clauses in operator MEL (not in master)
    for (const operatorClause of operatorClauses) {
      const masterClause = masterClauseMap.get(operatorClause.clauseNumber)
      
      if (!masterClause) {
        const difference = createAdditionalClauseDifference(operatorClause)
        differences.push(difference)
        
        const comparison = createClauseComparison(
          operatorClause,
          undefined,
          ComparisonStatus.ADDITIONAL,
          ['Clause not found in Master MEL'],
          IssueSeverity.MEDIUM
        )
        clauseComparisons.push(comparison)
        additionalCount++
      }
    }
    
    // Calculate compliance rate
    const totalClauses = Math.max(operatorClauses.length, masterClauses.length)
    const complianceRate = totalClauses > 0 ? (matchedCount / totalClauses) * 100 : 100
    
    const results: ComparisonResults = {
      totalOperatorClauses: operatorClauses.length,
      totalMasterClauses: masterClauses.length,
      matchedClauses: matchedCount,
      missingClauses: missingCount,
      modifiedClauses: modifiedCount,
      additionalClauses: additionalCount,
      conflictingClauses: conflictingCount,
      complianceRate: Math.round(complianceRate * 100) / 100,
      differences,
      clauseComparisons
    }
    
    const processingTime = Date.now() - startTime
    logger.info(`MEL comparison completed in ${processingTime}ms: ${matchedCount} matched, ${missingCount} missing, ${modifiedCount} modified, ${additionalCount} additional, ${conflictingCount} conflicting`)
    
    return results
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`MEL comparison failed after ${processingTime}ms:`, error)
    throw new Error(`Comparison failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Create a map of clauses indexed by clause number for efficient lookup
 */
function createClauseMap(clauses: MELClause[]): Map<string, MELClause> {
  const map = new Map<string, MELClause>()
  
  for (const clause of clauses) {
    map.set(clause.clauseNumber, clause)
  }
  
  return map
}

/**
 * Compare two individual clauses and detect differences
 */
function compareIndividualClauses(
  operatorClause: MELClause,
  masterClause: MELClause,
  options: ComparisonOptions
): ClauseComparison {
  const differences: string[] = []
  let severity = IssueSeverity.LOW
  let status = ComparisonStatus.MATCHED
  
  // Compare basic properties
  if (operatorClause.title !== masterClause.title) {
    differences.push(`Title differs: "${operatorClause.title}" vs "${masterClause.title}"`)
    severity = IssueSeverity.MEDIUM
    status = ComparisonStatus.MODIFIED
  }
  
  if (operatorClause.category !== masterClause.category) {
    differences.push(`Category differs: "${operatorClause.category}" vs "${masterClause.category}"`)
    severity = IssueSeverity.HIGH
    status = options.strictCategoryMatching ? ComparisonStatus.CONFLICTING : ComparisonStatus.MODIFIED
  }
  
  // Compare content (normalize whitespace for comparison)
  const operatorContent = normalizeContent(operatorClause.content)
  const masterContent = normalizeContent(masterClause.content)
  
  if (operatorContent !== masterContent) {
    const contentSimilarity = calculateContentSimilarity(operatorContent, masterContent)
    if (contentSimilarity < 0.8) {
      differences.push(`Content significantly differs (${Math.round(contentSimilarity * 100)}% similarity)`)
      severity = IssueSeverity.HIGH
      status = ComparisonStatus.MODIFIED
    } else if (contentSimilarity < 0.95) {
      differences.push(`Content has minor differences (${Math.round(contentSimilarity * 100)}% similarity)`)
      if (severity === IssueSeverity.LOW) severity = IssueSeverity.MEDIUM
      status = ComparisonStatus.MODIFIED
    }
  }
  
  // Compare conditions and limitations if enabled
  if (options.checkConditionsAndLimitations !== false) {
    const conditionDiffs = compareArrayFields(operatorClause.conditions, masterClause.conditions, 'conditions')
    const limitationDiffs = compareArrayFields(operatorClause.limitations, masterClause.limitations, 'limitations')
    
    differences.push(...conditionDiffs, ...limitationDiffs)
    if (conditionDiffs.length > 0 || limitationDiffs.length > 0) {
      if (severity === IssueSeverity.LOW) severity = IssueSeverity.MEDIUM
      status = ComparisonStatus.MODIFIED
    }
  }
  
  // Compare maintenance actions if enabled
  if (options.checkMaintenanceActions !== false) {
    const maintenanceDiffs = compareArrayFields(operatorClause.maintenanceActions, masterClause.maintenanceActions, 'maintenance actions')
    differences.push(...maintenanceDiffs)
    if (maintenanceDiffs.length > 0) {
      severity = IssueSeverity.HIGH
      status = ComparisonStatus.MODIFIED
    }
  }
  
  // Compare operational procedures if enabled
  if (options.checkOperationalProcedures !== false) {
    const procedureDiffs = compareArrayFields(operatorClause.operationalProcedures, masterClause.operationalProcedures, 'operational procedures')
    differences.push(...procedureDiffs)
    if (procedureDiffs.length > 0) {
      if (severity === IssueSeverity.LOW) severity = IssueSeverity.MEDIUM
      status = ComparisonStatus.MODIFIED
    }
  }
  
  // Compare placard requirements
  if (operatorClause.placard !== masterClause.placard) {
    differences.push(`Placard differs: "${operatorClause.placard || 'none'}" vs "${masterClause.placard || 'none'}"`)
    severity = IssueSeverity.MEDIUM
    status = ComparisonStatus.MODIFIED
  }
  
  return createClauseComparison(operatorClause, masterClause, status, differences, severity)
}

/**
 * Compare array fields and return differences
 */
function compareArrayFields(operatorArray: string[] | undefined, masterArray: string[] | undefined, fieldName: string): string[] {
  const differences: string[] = []

  const opArray = operatorArray || []
  const masterArray_ = masterArray || []

  // Check for missing items
  for (const masterItem of masterArray_) {
    if (!opArray.includes(masterItem)) {
      differences.push(`Missing ${fieldName}: "${masterItem}"`)
    }
  }

  // Check for additional items
  for (const opItem of opArray) {
    if (!masterArray_.includes(opItem)) {
      differences.push(`Additional ${fieldName}: "${opItem}"`)
    }
  }

  return differences
}

/**
 * Normalize content for comparison (remove extra whitespace, standardize formatting)
 */
function normalizeContent(content: string): string {
  if (!content) return ''

  return content
    .replace(/\s+/g, ' ')  // Replace multiple whitespace with single space
    .replace(/[""]/g, '"') // Normalize quotes
    .replace(/['']/g, "'") // Normalize apostrophes
    .trim()
    .toLowerCase()
}

/**
 * Calculate content similarity using simple word-based comparison
 */
function calculateContentSimilarity(content1: string, content2: string): number {
  if (content1 === content2) return 1.0
  if (!content1 || !content2) return 0.0

  const words1 = content1.split(/\s+/).filter(w => w.length > 0)
  const words2 = content2.split(/\s+/).filter(w => w.length > 0)

  if (words1.length === 0 && words2.length === 0) return 1.0
  if (words1.length === 0 || words2.length === 0) return 0.0

  const commonWords = words1.filter(word => words2.includes(word))
  const totalWords = Math.max(words1.length, words2.length)

  return commonWords.length / totalWords
}

/**
 * Create a clause comparison object
 */
function createClauseComparison(
  operatorClause: MELClause | undefined,
  masterClause: MELClause | undefined,
  status: ComparisonStatus,
  differences: string[],
  severity: IssueSeverity
): ClauseComparison {
  let recommendation = ''

  switch (status) {
    case ComparisonStatus.MISSING:
      recommendation = 'Add this clause to the Operator MEL or provide justification for omission'
      break
    case ComparisonStatus.MODIFIED:
      recommendation = severity === IssueSeverity.HIGH
        ? 'Review and align with Master MEL requirements'
        : 'Consider aligning with Master MEL for consistency'
      break
    case ComparisonStatus.ADDITIONAL:
      recommendation = 'Verify this clause is appropriate and properly authorized'
      break
    case ComparisonStatus.CONFLICTING:
      recommendation = 'Resolve conflict with Master MEL requirements immediately'
      break
    case ComparisonStatus.MATCHED:
      recommendation = 'Clause is compliant'
      break
  }

  return {
    operatorClause: operatorClause!,
    masterClause,
    status,
    differences,
    severity,
    recommendation
  }
}

/**
 * Create a missing clause difference
 */
function createMissingClauseDifference(masterClause: MELClause): MELDifference {
  return {
    ataChapter: masterClause.section || extractATAChapter(masterClause.clauseNumber),
    clauseNumber: masterClause.clauseNumber,
    differenceType: 'missing',
    mmelClause: masterClause,
    severity: IssueSeverity.HIGH,
    differences: ['Clause is present in Master MEL but missing from Operator MEL'],
    recommendation: 'Add this clause to the Operator MEL or provide regulatory justification for omission'
  }
}

/**
 * Create a modified clause difference
 */
function createModifiedClauseDifference(operatorClause: MELClause, masterClause: MELClause, differences: string[]): MELDifference {
  const severity = determineSeverityFromDifferences(differences)

  return {
    ataChapter: operatorClause.section || extractATAChapter(operatorClause.clauseNumber),
    clauseNumber: operatorClause.clauseNumber,
    differenceType: 'modified',
    operatorClause,
    mmelClause: masterClause,
    severity,
    differences,
    recommendation: severity === IssueSeverity.HIGH
      ? 'Review modifications against Master MEL and ensure regulatory compliance'
      : 'Consider aligning with Master MEL for consistency'
  }
}

/**
 * Create an additional clause difference
 */
function createAdditionalClauseDifference(operatorClause: MELClause): MELDifference {
  return {
    ataChapter: operatorClause.section || extractATAChapter(operatorClause.clauseNumber),
    clauseNumber: operatorClause.clauseNumber,
    differenceType: 'additional',
    operatorClause,
    severity: IssueSeverity.MEDIUM,
    differences: ['Clause is present in Operator MEL but not in Master MEL'],
    recommendation: 'Verify this additional clause is properly authorized and compliant with regulations'
  }
}

/**
 * Create a conflicting clause difference
 */
function createConflictingClauseDifference(operatorClause: MELClause, masterClause: MELClause, differences: string[]): MELDifference {
  return {
    ataChapter: operatorClause.section || extractATAChapter(operatorClause.clauseNumber),
    clauseNumber: operatorClause.clauseNumber,
    differenceType: 'conflicting',
    operatorClause,
    mmelClause: masterClause,
    severity: IssueSeverity.CRITICAL,
    differences,
    recommendation: 'Resolve conflict immediately - conflicting requirements may compromise safety'
  }
}

/**
 * Extract ATA chapter from clause number
 */
function extractATAChapter(clauseNumber: string): string {
  const match = clauseNumber.match(/^(\d{2}-\d{2})/)
  return match ? match[1] : 'unknown'
}

/**
 * Determine severity based on the types of differences found
 */
function determineSeverityFromDifferences(differences: string[]): IssueSeverity {
  const diffText = differences.join(' ').toLowerCase()

  if (diffText.includes('category differs') || diffText.includes('maintenance') || diffText.includes('significantly differs')) {
    return IssueSeverity.HIGH
  }

  if (diffText.includes('title differs') || diffText.includes('placard') || diffText.includes('conditions') || diffText.includes('limitations')) {
    return IssueSeverity.MEDIUM
  }

  return IssueSeverity.LOW
}
