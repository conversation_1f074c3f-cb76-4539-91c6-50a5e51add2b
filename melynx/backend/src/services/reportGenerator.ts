import PDFDocument from 'pdfkit'
import { supabase } from '../utils/supabase.js'
import { logger } from '../utils/logger.js'
import { getAnalysisResults } from './analysisService.js'
import { getDocument } from './storageService.js'
import { AnalysisResults, MELAnalysis, ComplianceIssue, ClauseComparison } from '../../../shared/src/types/index.js'

/**
 * Report Generator Service
 * Generates PDF and JSON reports for MEL compliance analysis
 */

export interface ReportOptions {
  includeDetailedFindings?: boolean
  includeAuditTrail?: boolean
  includeSignatureBlock?: boolean
  inspectorName?: string
  inspectorLicense?: string
}

export interface ReportData {
  analysis: MELAnalysis
  results: AnalysisResults
  operatorDocument: any
  masterDocument: any
  reportOptions: ReportOptions
}

/**
 * Generate PDF report for MEL compliance analysis
 */
export async function generatePDFReport(
  analysisId: string,
  options: ReportOptions = {}
): Promise<Buffer> {
  const startTime = Date.now()
  
  try {
    logger.info(`Generating PDF report for analysis ${analysisId}`)
    
    // Get analysis data
    const reportData = await getReportData(analysisId, options)
    
    // Create PDF document
    const doc = new PDFDocument({ 
      size: 'A4', 
      margin: 50,
      info: {
        Title: `MEL Compliance Report - ${reportData.operatorDocument.metadata.aircraftType}`,
        Author: 'MELynx AI Compliance Inspector',
        Subject: 'MEL Compliance Analysis Report',
        Creator: 'MELynx',
        Producer: 'MELynx Report Generator'
      }
    })
    
    // Generate report content
    await generatePDFContent(doc, reportData)
    
    // Convert to buffer
    const chunks: Buffer[] = []
    doc.on('data', chunk => chunks.push(chunk))
    doc.on('end', () => {})
    doc.end()
    
    const pdfBuffer = Buffer.concat(chunks)
    
    const processingTime = Date.now() - startTime
    logger.info(`PDF report generated in ${processingTime}ms (${pdfBuffer.length} bytes)`)
    
    return pdfBuffer
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`PDF report generation failed after ${processingTime}ms:`, error)
    throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Generate JSON report for MEL compliance analysis
 */
export async function generateJSONReport(
  analysisId: string,
  options: ReportOptions = {}
): Promise<object> {
  const startTime = Date.now()
  
  try {
    logger.info(`Generating JSON report for analysis ${analysisId}`)
    
    // Get analysis data
    const reportData = await getReportData(analysisId, options)
    
    // Create structured JSON report
    const jsonReport = {
      metadata: {
        reportId: `report_${analysisId}_${Date.now()}`,
        generatedAt: new Date().toISOString(),
        generatedBy: 'MELynx AI Compliance Inspector',
        version: '1.0.0',
        analysisId: analysisId
      },
      
      aircraft: {
        type: reportData.operatorDocument.metadata.aircraftType,
        operator: reportData.operatorDocument.metadata.operator,
        tailNumber: reportData.operatorDocument.metadata.tailNumber
      },
      
      documents: {
        operatorMEL: {
          id: reportData.operatorDocument.id,
          filename: reportData.operatorDocument.originalName,
          effectiveDate: reportData.operatorDocument.metadata.effectiveDate,
          revisionNumber: reportData.operatorDocument.metadata.revisionNumber
        },
        masterMEL: {
          id: reportData.masterDocument.id,
          filename: reportData.masterDocument.originalName,
          effectiveDate: reportData.masterDocument.metadata.effectiveDate,
          revisionNumber: reportData.masterDocument.metadata.revisionNumber
        }
      },
      
      analysis: {
        startedAt: reportData.analysis.startedAt,
        completedAt: reportData.analysis.completedAt,
        processingTime: reportData.analysis.completedAt 
          ? new Date(reportData.analysis.completedAt).getTime() - new Date(reportData.analysis.startedAt).getTime()
          : null,
        options: reportData.analysis.options
      },
      
      complianceResults: {
        overallRate: reportData.results.complianceRate,
        totalClauses: reportData.results.totalClauses,
        matchedClauses: reportData.results.matchedClauses,
        missingClauses: reportData.results.missingClauses,
        modifiedClauses: reportData.results.modifiedClauses,
        additionalClauses: reportData.results.additionalClauses,
        
        assessment: getComplianceAssessment(reportData.results.complianceRate),
        recommendation: getInspectorRecommendation(reportData.results)
      },
      
      findings: {
        criticalIssues: reportData.results.criticalIssues,
        warnings: reportData.results.warnings,
        summary: reportData.results.summary,
        recommendations: reportData.results.recommendations
      },
      
      clauseDetails: options.includeDetailedFindings ? reportData.results.clauseComparisons : undefined,
      
      inspector: {
        name: options.inspectorName,
        license: options.inspectorLicense,
        reviewedAt: new Date().toISOString()
      },
      
      auditTrail: options.includeAuditTrail ? await getAuditTrail(analysisId) : undefined
    }
    
    const processingTime = Date.now() - startTime
    logger.info(`JSON report generated in ${processingTime}ms`)
    
    return jsonReport
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`JSON report generation failed after ${processingTime}ms:`, error)
    throw new Error(`JSON generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Save report to Supabase storage
 */
export async function saveReportToStorage(
  analysisId: string,
  reportBuffer: Buffer,
  format: 'pdf' | 'json',
  filename?: string
): Promise<string> {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const defaultFilename = `${analysisId}_report_${timestamp}.${format}`
    const finalFilename = filename || defaultFilename
    const storagePath = `reports/${finalFilename}`
    
    const { data, error } = await supabase.storage
      .from('melynx-reports')
      .upload(storagePath, reportBuffer, {
        contentType: format === 'pdf' ? 'application/pdf' : 'application/json',
        duplex: 'half'
      })
    
    if (error) {
      throw new Error(`Storage upload failed: ${error.message}`)
    }
    
    logger.info(`Report saved to storage: ${storagePath}`)
    return storagePath
    
  } catch (error) {
    logger.error(`Failed to save report to storage:`, error)
    throw error
  }
}

/**
 * Get comprehensive report data
 */
async function getReportData(analysisId: string, options: ReportOptions): Promise<ReportData> {
  try {
    // Get analysis record
    const { data: analysisData, error: analysisError } = await supabase
      .from('mel_evaluations')
      .select('*')
      .eq('id', analysisId)
      .single()
    
    if (analysisError || !analysisData) {
      throw new Error(`Analysis not found: ${analysisId}`)
    }
    
    // Get analysis results
    const results = await getAnalysisResults(analysisId)
    
    // Get document details
    const operatorDocument = await getDocument(analysisData.operator_mel_id)
    const masterDocument = await getDocument(analysisData.master_mel_id)
    
    if (!operatorDocument || !masterDocument) {
      throw new Error('Document data not found')
    }
    
    const analysis: MELAnalysis = {
      id: analysisData.id,
      operatorMelId: analysisData.operator_mel_id,
      masterMelId: analysisData.master_mel_id,
      status: analysisData.status,
      startedAt: analysisData.started_at,
      completedAt: analysisData.completed_at,
      results,
      options: {
        clauseByClauseComparison: analysisData.clause_by_clause_comparison,
        complianceGapAnalysis: analysisData.compliance_gap_analysis,
        generateDetailedReport: analysisData.generate_detailed_report,
        enableRealtimeChat: analysisData.enable_realtime_chat,
        includeRecommendations: analysisData.include_recommendations
      },
      createdBy: analysisData.created_by
    }
    
    return {
      analysis,
      results,
      operatorDocument,
      masterDocument,
      reportOptions: options
    }
    
  } catch (error) {
    logger.error(`Failed to get report data for ${analysisId}:`, error)
    throw error
  }
}

/**
 * Generate PDF content
 */
async function generatePDFContent(doc: PDFKit.PDFDocument, reportData: ReportData): Promise<void> {
  // Cover page
  generateCoverPage(doc, reportData)

  // Executive summary
  doc.addPage()
  generateExecutiveSummary(doc, reportData)

  // Compliance statistics
  doc.addPage()
  generateComplianceStatistics(doc, reportData)

  // Detailed findings
  if (reportData.reportOptions.includeDetailedFindings !== false) {
    doc.addPage()
    generateDetailedFindings(doc, reportData)
  }

  // Signature block
  if (reportData.reportOptions.includeSignatureBlock !== false) {
    doc.addPage()
    generateSignatureBlock(doc, reportData)
  }
}

/**
 * Generate cover page
 */
function generateCoverPage(doc: PDFKit.PDFDocument, reportData: ReportData): void {
  const { analysis, operatorDocument, results } = reportData

  // MELynx header
  doc.fontSize(24)
     .fillColor('#0066cc')
     .text('MELynx', 50, 80)
     .fontSize(16)
     .fillColor('#666666')
     .text('AI-Powered MEL Compliance Inspector', 50, 110)

  // Title
  doc.fontSize(20)
     .fillColor('#000000')
     .text('MEL COMPLIANCE ANALYSIS REPORT', 50, 180, { align: 'center' })

  // Aircraft info
  doc.fontSize(14)
     .text(`Aircraft Type: ${operatorDocument.metadata.aircraftType || 'Unknown'}`, 50, 240)
     .text(`Operator: ${operatorDocument.metadata.operator || 'Unknown'}`, 50, 260)
     .text(`Analysis ID: ${analysis.id}`, 50, 280)
     .text(`Report Date: ${new Date().toLocaleDateString()}`, 50, 300)

  // Compliance summary box
  const complianceRate = results.complianceRate
  const color = complianceRate >= 95 ? '#28a745' : complianceRate >= 85 ? '#ffc107' : '#dc3545'

  doc.rect(50, 350, 500, 100)
     .stroke()
     .fontSize(16)
     .fillColor(color)
     .text(`Overall Compliance Rate: ${complianceRate}%`, 70, 380)
     .fillColor('#000000')
     .fontSize(12)
     .text(`${results.matchedClauses} of ${results.totalClauses} clauses compliant`, 70, 410)
     .text(`${results.criticalIssues.length} critical issues identified`, 70, 430)

  // Footer
  doc.fontSize(10)
     .fillColor('#666666')
     .text('Generated by MELynx AI Compliance Inspector', 50, 750, { align: 'center' })
     .text(`Report ID: report_${analysis.id}_${Date.now()}`, 50, 765, { align: 'center' })
}

/**
 * Generate executive summary
 */
function generateExecutiveSummary(doc: PDFKit.PDFDocument, reportData: ReportData): void {
  const { results } = reportData

  doc.fontSize(18)
     .fillColor('#000000')
     .text('EXECUTIVE SUMMARY', 50, 80)

  doc.fontSize(12)
     .text(results.summary || 'No summary available', 50, 120, {
       width: 500,
       align: 'justify'
     })

  // Critical issues
  if (results.criticalIssues.length > 0) {
    doc.fontSize(14)
       .fillColor('#dc3545')
       .text('CRITICAL ISSUES', 50, 220)

    let yPos = 250
    results.criticalIssues.forEach((issue, index) => {
      doc.fontSize(12)
         .fillColor('#000000')
         .text(`${index + 1}. ${issue.title}`, 70, yPos)
         .text(issue.description, 90, yPos + 15, { width: 450 })
         .text(`Recommendation: ${issue.recommendation}`, 90, yPos + 40, { width: 450 })

      yPos += 80

      if (yPos > 700) {
        doc.addPage()
        yPos = 80
      }
    })
  }

  // Recommendations
  if (results.recommendations.length > 0) {
    let currentYPos = 500 // Fixed position for recommendations
    doc.fontSize(14)
       .fillColor('#0066cc')
       .text('RECOMMENDATIONS', 50, currentYPos)

    let recYPos = currentYPos + 30
    results.recommendations.forEach((rec, index) => {
      doc.fontSize(12)
         .fillColor('#000000')
         .text(`${index + 1}. ${rec}`, 70, recYPos, { width: 450 })

      recYPos += 25
    })
  }
}

/**
 * Generate compliance statistics
 */
function generateComplianceStatistics(doc: PDFKit.PDFDocument, reportData: ReportData): void {
  const { results } = reportData

  doc.fontSize(18)
     .fillColor('#000000')
     .text('COMPLIANCE STATISTICS', 50, 80)

  // Statistics table
  const stats = [
    ['Total Clauses', results.totalClauses.toString()],
    ['Matched Clauses', results.matchedClauses.toString()],
    ['Missing Clauses', results.missingClauses.toString()],
    ['Modified Clauses', results.modifiedClauses.toString()],
    ['Additional Clauses', results.additionalClauses.toString()],
    ['Compliance Rate', `${results.complianceRate}%`]
  ]

  let yPos = 120
  stats.forEach(([label, value]) => {
    doc.fontSize(12)
       .fillColor('#000000')
       .text(label, 70, yPos)
       .text(value, 300, yPos)

    yPos += 25
  })

  // Visual compliance chart (simple bar representation)
  const chartY = 300
  const chartWidth = 400
  const chartHeight = 20

  doc.fontSize(14)
     .text('Compliance Breakdown', 50, chartY - 30)

  // Background bar
  doc.rect(70, chartY, chartWidth, chartHeight)
     .fillAndStroke('#f8f9fa', '#dee2e6')

  // Compliance bar
  const complianceWidth = (results.complianceRate / 100) * chartWidth
  const color = results.complianceRate >= 95 ? '#28a745' : results.complianceRate >= 85 ? '#ffc107' : '#dc3545'

  doc.rect(70, chartY, complianceWidth, chartHeight)
     .fillAndStroke(color, color)

  // Percentage label
  doc.fontSize(12)
     .fillColor('#000000')
     .text(`${results.complianceRate}%`, 70 + complianceWidth + 10, chartY + 5)
}

/**
 * Generate detailed findings
 */
function generateDetailedFindings(doc: PDFKit.PDFDocument, reportData: ReportData): void {
  const { results } = reportData

  doc.fontSize(18)
     .fillColor('#000000')
     .text('DETAILED CLAUSE ANALYSIS', 50, 80)

  if (!results.clauseComparisons || results.clauseComparisons.length === 0) {
    doc.fontSize(12)
       .text('No detailed clause comparisons available', 50, 120)
    return
  }

  // Table headers
  doc.fontSize(10)
     .fillColor('#666666')
     .text('Clause', 50, 120)
     .text('Title', 120, 120)
     .text('Status', 300, 120)
     .text('Category', 380, 120)
     .text('Issues', 450, 120)

  // Draw header line
  doc.moveTo(50, 135)
     .lineTo(550, 135)
     .stroke()

  let yPos = 150

  results.clauseComparisons.forEach((comparison, index) => {
    if (yPos > 700) {
      doc.addPage()
      yPos = 80
    }

    const clause = comparison.operatorClause
    const status = comparison.status
    const statusColor = getStatusColor(status)

    doc.fontSize(9)
       .fillColor('#000000')
       .text(clause.clauseNumber, 50, yPos)
       .text(clause.title.substring(0, 25) + '...', 120, yPos)
       .fillColor(statusColor)
       .text(status.toUpperCase(), 300, yPos)
       .fillColor('#000000')
       .text(clause.category, 380, yPos)
       .text(comparison.differences.length.toString(), 450, yPos)

    yPos += 15
  })
}

/**
 * Generate signature block
 */
function generateSignatureBlock(doc: PDFKit.PDFDocument, reportData: ReportData): void {
  const { reportOptions } = reportData

  doc.fontSize(18)
     .fillColor('#000000')
     .text('INSPECTOR CERTIFICATION', 50, 80)

  doc.fontSize(12)
     .text('I certify that I have reviewed this MEL compliance analysis and the findings contained herein.', 50, 120, {
       width: 500,
       align: 'justify'
     })

  // Inspector info
  doc.fontSize(12)
     .text(`Inspector Name: ${reportOptions.inspectorName || '_________________________'}`, 50, 200)
     .text(`License Number: ${reportOptions.inspectorLicense || '_________________________'}`, 50, 230)
     .text(`Date: ${new Date().toLocaleDateString()}`, 50, 260)

  // Signature line
  doc.text('Signature: _________________________', 50, 320)

  // Recommendation
  const recommendation = getInspectorRecommendation(reportData.results)
  const recColor = recommendation === 'APPROVE' ? '#28a745' : recommendation === 'REJECT' ? '#dc3545' : '#ffc107'

  doc.fontSize(14)
     .fillColor(recColor)
     .text(`RECOMMENDATION: ${recommendation}`, 50, 380)

  // Footer
  doc.fontSize(10)
     .fillColor('#666666')
     .text('This report was generated by MELynx AI Compliance Inspector', 50, 750, { align: 'center' })
}

/**
 * Helper functions
 */
function getComplianceAssessment(complianceRate: number): string {
  if (complianceRate >= 95) return 'COMPLIANT'
  if (complianceRate >= 85) return 'NEEDS_REVIEW'
  return 'NON_COMPLIANT'
}

function getInspectorRecommendation(results: AnalysisResults): string {
  if (results.criticalIssues.length > 0) return 'REJECT'
  if (results.complianceRate < 85) return 'REJECT'
  if (results.complianceRate < 95) return 'CONDITIONAL_APPROVAL'
  return 'APPROVE'
}

function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'matched': return '#28a745'
    case 'modified': return '#ffc107'
    case 'missing': return '#dc3545'
    case 'additional': return '#17a2b8'
    case 'conflicting': return '#dc3545'
    default: return '#6c757d'
  }
}

async function getAuditTrail(analysisId: string): Promise<any[]> {
  try {
    const { data, error } = await supabase
      .from('mel_audit_log')
      .select('*')
      .eq('analysis_id', analysisId)
      .order('created_at', { ascending: false })

    if (error) {
      logger.warn(`Failed to get audit trail for ${analysisId}:`, error)
      return []
    }

    return data || []
  } catch (error) {
    logger.warn(`Error getting audit trail:`, error)
    return []
  }
}
