import axios from 'axios'
import { AnalysisResults, ComplianceIssue, IssueSeverity, IssueType } from '../../../shared/src/types/index.js'
import { MELDifference, ComparisonResults } from '../../../shared/src/types/index.js'
import { logger } from '../utils/logger.js'

/**
 * AI Compliance Summarizer Service
 * Uses OpenRouter API with Meta LLaMA 3.3 to analyze MEL compliance differences
 * and generate inspector-ready summaries and recommendations
 */

export interface AIAnalysisRequest {
  differences: MELDifference[]
  comparisonResults: ComparisonResults
  aircraftType: string
  operatorName?: string
  inspectorName?: string
  analysisOptions?: {
    includeRecommendations?: boolean
    focusOnCriticalIssues?: boolean
    includeRegulatoryReferences?: boolean
  }
}

export interface AIAnalysisResponse {
  summary: string
  complianceAssessment: 'COMPLIANT' | 'NEEDS_REVIEW' | 'NON_COMPLIANT'
  criticalIssues: ComplianceIssue[]
  warnings: ComplianceIssue[]
  recommendations: string[]
  inspectorRecommendation: 'APPROVE' | 'CONDITIONAL_APPROVAL' | 'REJECT'
  confidence: number
  processingTime: number
}

const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions'
const MODEL_NAME = process.env.OPENROUTER_MODEL || 'meta-llama/llama-3.3-70b-instruct'

/**
 * Main AI analysis function
 * Analyzes MEL differences and generates compliance summary
 */
export async function generateComplianceAnalysis(request: AIAnalysisRequest): Promise<AIAnalysisResponse> {
  const startTime = Date.now()
  
  try {
    logger.info(`Starting AI compliance analysis for ${request.aircraftType} with ${request.differences.length} differences`)
    
    // Validate API key
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OpenRouter API key not configured')
    }
    
    // Prepare the analysis prompt
    const systemPrompt = createSystemPrompt(request)
    const userPrompt = createUserPrompt(request)
    
    // Call OpenRouter API
    const aiResponse = await callOpenRouterAPI(systemPrompt, userPrompt)
    
    // Parse and structure the response
    const analysisResponse = parseAIResponse(aiResponse, request)
    
    const processingTime = Date.now() - startTime
    analysisResponse.processingTime = processingTime
    
    logger.info(`AI compliance analysis completed in ${processingTime}ms with ${analysisResponse.complianceAssessment} assessment`)
    
    return analysisResponse
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`AI compliance analysis failed after ${processingTime}ms:`, error)
    throw new Error(`AI analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Create system prompt for the AI model
 */
function createSystemPrompt(request: AIAnalysisRequest): string {
  return `You are MELynx, an expert aviation MEL (Minimum Equipment List) compliance inspector with deep knowledge of:
- FAA, EASA, ICAO, and TCCA regulations
- Aircraft systems and ATA chapters
- MEL compliance requirements and best practices
- Aviation safety standards

Your role is to analyze differences between Operator MEL and Master MEL (MMEL) documents and provide professional compliance assessments.

ANALYSIS GUIDELINES:
1. Focus on safety-critical differences that could impact flight operations
2. Consider regulatory compliance requirements
3. Evaluate operational impact and maintenance implications
4. Provide clear, actionable recommendations for inspectors
5. Reference specific ATA chapters and clause numbers
6. Classify issues by severity: CRITICAL, HIGH, MEDIUM, LOW, INFO

RESPONSE FORMAT:
Provide a structured analysis in JSON format with:
- summary: Executive summary of findings
- complianceAssessment: Overall assessment (COMPLIANT/NEEDS_REVIEW/NON_COMPLIANT)
- criticalIssues: Array of critical compliance issues
- warnings: Array of warning-level issues
- recommendations: Array of specific recommendations
- inspectorRecommendation: Final recommendation (APPROVE/CONDITIONAL_APPROVAL/REJECT)
- confidence: Confidence level (0-100)

Be thorough, professional, and safety-focused in your analysis.`
}

/**
 * Create user prompt with specific analysis data
 */
function createUserPrompt(request: AIAnalysisRequest): string {
  const { differences, comparisonResults, aircraftType, operatorName, inspectorName } = request
  
  let prompt = `COMPLIANCE ANALYSIS REQUEST

Aircraft Type: ${aircraftType}
${operatorName ? `Operator: ${operatorName}` : ''}
${inspectorName ? `Inspector: ${inspectorName}` : ''}

COMPARISON SUMMARY:
- Total Operator MEL Clauses: ${comparisonResults.totalOperatorClauses}
- Total Master MEL Clauses: ${comparisonResults.totalMasterClauses}
- Matched Clauses: ${comparisonResults.matchedClauses}
- Missing Clauses: ${comparisonResults.missingClauses}
- Modified Clauses: ${comparisonResults.modifiedClauses}
- Additional Clauses: ${comparisonResults.additionalClauses}
- Conflicting Clauses: ${comparisonResults.conflictingClauses}
- Current Compliance Rate: ${comparisonResults.complianceRate}%

DETAILED DIFFERENCES ANALYSIS:
`

  // Group differences by ATA chapter for better organization
  const differencesByChapter = groupDifferencesByChapter(differences)
  
  for (const [chapter, chapterDifferences] of Object.entries(differencesByChapter)) {
    prompt += `\nATA CHAPTER ${chapter}:\n`
    
    for (const diff of chapterDifferences) {
      prompt += `
Clause ${diff.clauseNumber} (${diff.differenceType.toUpperCase()}):
- Severity: ${diff.severity}
- Differences: ${diff.differences.join('; ')}
- Current Recommendation: ${diff.recommendation}
`
      
      if (diff.mmelClause) {
        prompt += `- Master MEL: "${diff.mmelClause.title}" (Category ${diff.mmelClause.category})\n`
      }
      
      if (diff.operatorClause) {
        prompt += `- Operator MEL: "${diff.operatorClause.title}" (Category ${diff.operatorClause.category})\n`
      }
    }
  }
  
  prompt += `\nPLEASE ANALYZE:
1. Overall compliance status and safety implications
2. Critical issues requiring immediate attention
3. Regulatory compliance concerns
4. Operational impact assessment
5. Specific recommendations for resolution
6. Final inspector recommendation

Provide your analysis in the specified JSON format.`
  
  return prompt
}

/**
 * Call OpenRouter API with the prepared prompts
 */
async function callOpenRouterAPI(systemPrompt: string, userPrompt: string): Promise<string> {
  try {
    const response = await axios.post(
      OPENROUTER_API_URL,
      {
        model: MODEL_NAME,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.1, // Low temperature for consistent, factual responses
        top_p: 0.9,
        frequency_penalty: 0.0,
        presence_penalty: 0.0
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://melynx.com',
          'X-Title': 'MELynx AI Compliance Inspector'
        },
        timeout: 60000 // 60 second timeout
      }
    )
    
    if (!response.data.choices || response.data.choices.length === 0) {
      throw new Error('No response from AI model')
    }
    
    const aiResponse = response.data.choices[0].message.content
    logger.debug(`AI response received: ${aiResponse.length} characters`)
    
    return aiResponse
    
  } catch (error) {
    if (axios.isAxiosError(error)) {
      logger.error('OpenRouter API error:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      })
      throw new Error(`OpenRouter API error: ${error.response?.status} ${error.response?.statusText}`)
    }
    
    logger.error('OpenRouter API call failed:', error)
    throw error
  }
}

/**
 * Parse AI response and structure it according to our interface
 */
function parseAIResponse(aiResponse: string, request: AIAnalysisRequest): AIAnalysisResponse {
  try {
    // Try to extract JSON from the response
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
    let parsedResponse: any
    
    if (jsonMatch) {
      parsedResponse = JSON.parse(jsonMatch[0])
    } else {
      // Fallback: create structured response from text
      parsedResponse = createFallbackResponse(aiResponse, request)
    }
    
    // Validate and structure the response
    const analysisResponse: AIAnalysisResponse = {
      summary: parsedResponse.summary || 'AI analysis completed',
      complianceAssessment: validateComplianceAssessment(parsedResponse.complianceAssessment),
      criticalIssues: parseComplianceIssues(parsedResponse.criticalIssues, IssueType.REGULATORY_COMPLIANCE, IssueSeverity.CRITICAL),
      warnings: parseComplianceIssues(parsedResponse.warnings, IssueType.REGULATORY_COMPLIANCE, IssueSeverity.MEDIUM),
      recommendations: Array.isArray(parsedResponse.recommendations) ? parsedResponse.recommendations : [],
      inspectorRecommendation: validateInspectorRecommendation(parsedResponse.inspectorRecommendation),
      confidence: Math.min(100, Math.max(0, parsedResponse.confidence || 85)),
      processingTime: 0 // Will be set by caller
    }
    
    return analysisResponse
    
  } catch (error) {
    logger.error('Failed to parse AI response:', error)
    
    // Return a safe fallback response
    return createFallbackAnalysisResponse(request)
  }
}

/**
 * Group differences by ATA chapter for organized analysis
 */
function groupDifferencesByChapter(differences: MELDifference[]): Record<string, MELDifference[]> {
  const grouped: Record<string, MELDifference[]> = {}

  for (const diff of differences) {
    const chapter = diff.ataChapter
    if (!grouped[chapter]) {
      grouped[chapter] = []
    }
    grouped[chapter].push(diff)
  }

  return grouped
}

/**
 * Validate compliance assessment value
 */
function validateComplianceAssessment(assessment: any): 'COMPLIANT' | 'NEEDS_REVIEW' | 'NON_COMPLIANT' {
  const validAssessments = ['COMPLIANT', 'NEEDS_REVIEW', 'NON_COMPLIANT']

  if (typeof assessment === 'string' && validAssessments.includes(assessment.toUpperCase())) {
    return assessment.toUpperCase() as 'COMPLIANT' | 'NEEDS_REVIEW' | 'NON_COMPLIANT'
  }

  return 'NEEDS_REVIEW' // Safe default
}

/**
 * Validate inspector recommendation value
 */
function validateInspectorRecommendation(recommendation: any): 'APPROVE' | 'CONDITIONAL_APPROVAL' | 'REJECT' {
  const validRecommendations = ['APPROVE', 'CONDITIONAL_APPROVAL', 'REJECT']

  if (typeof recommendation === 'string' && validRecommendations.includes(recommendation.toUpperCase())) {
    return recommendation.toUpperCase() as 'APPROVE' | 'CONDITIONAL_APPROVAL' | 'REJECT'
  }

  return 'CONDITIONAL_APPROVAL' // Safe default
}

/**
 * Parse compliance issues from AI response
 */
function parseComplianceIssues(issues: any, defaultType: IssueType, defaultSeverity: IssueSeverity): ComplianceIssue[] {
  if (!Array.isArray(issues)) {
    return []
  }

  return issues.map((issue, index) => ({
    id: `ai_issue_${Date.now()}_${index}`,
    type: issue.type || defaultType,
    severity: issue.severity || defaultSeverity,
    clauseId: issue.clauseId || 'unknown',
    title: issue.title || 'Compliance Issue',
    description: issue.description || 'Issue identified during AI analysis',
    recommendation: issue.recommendation || 'Review and address this issue',
    regulatoryReference: issue.regulatoryReference
  }))
}

/**
 * Create fallback response when AI response cannot be parsed
 */
function createFallbackResponse(aiResponse: string, request: AIAnalysisRequest): any {
  const criticalCount = request.differences.filter(d => d.severity === IssueSeverity.CRITICAL).length
  const highCount = request.differences.filter(d => d.severity === IssueSeverity.HIGH).length

  let complianceAssessment = 'COMPLIANT'
  let inspectorRecommendation = 'APPROVE'

  if (criticalCount > 0) {
    complianceAssessment = 'NON_COMPLIANT'
    inspectorRecommendation = 'REJECT'
  } else if (highCount > 0 || request.comparisonResults.complianceRate < 90) {
    complianceAssessment = 'NEEDS_REVIEW'
    inspectorRecommendation = 'CONDITIONAL_APPROVAL'
  }

  return {
    summary: `Analysis completed for ${request.aircraftType}. Found ${request.differences.length} differences with ${criticalCount} critical and ${highCount} high severity issues.`,
    complianceAssessment,
    criticalIssues: [],
    warnings: [],
    recommendations: [
      'Review all identified differences',
      'Address critical and high severity issues',
      'Ensure regulatory compliance'
    ],
    inspectorRecommendation,
    confidence: 75
  }
}

/**
 * Create fallback analysis response when parsing completely fails
 */
function createFallbackAnalysisResponse(request: AIAnalysisRequest): AIAnalysisResponse {
  const criticalCount = request.differences.filter(d => d.severity === IssueSeverity.CRITICAL).length
  const highCount = request.differences.filter(d => d.severity === IssueSeverity.HIGH).length

  return {
    summary: `MEL compliance analysis completed for ${request.aircraftType}. Identified ${request.differences.length} differences requiring review.`,
    complianceAssessment: criticalCount > 0 ? 'NON_COMPLIANT' : (highCount > 0 ? 'NEEDS_REVIEW' : 'COMPLIANT'),
    criticalIssues: [],
    warnings: [],
    recommendations: [
      'Manual review required due to AI processing error',
      'Examine all identified differences carefully',
      'Consult regulatory guidance for complex issues'
    ],
    inspectorRecommendation: criticalCount > 0 ? 'REJECT' : 'CONDITIONAL_APPROVAL',
    confidence: 50,
    processingTime: 0
  }
}

/**
 * Convert comparison results to analysis results format
 */
export function convertToAnalysisResults(
  comparisonResults: ComparisonResults,
  aiAnalysis: AIAnalysisResponse
): AnalysisResults {
  return {
    complianceRate: comparisonResults.complianceRate,
    totalClauses: comparisonResults.totalMasterClauses,
    matchedClauses: comparisonResults.matchedClauses,
    missingClauses: comparisonResults.missingClauses,
    modifiedClauses: comparisonResults.modifiedClauses,
    additionalClauses: comparisonResults.additionalClauses,
    criticalIssues: aiAnalysis.criticalIssues,
    warnings: aiAnalysis.warnings,
    recommendations: aiAnalysis.recommendations,
    summary: aiAnalysis.summary,
    clauseComparisons: comparisonResults.clauseComparisons
  }
}

/**
 * Generate a simple text summary for quick review
 */
export function generateQuickSummary(comparisonResults: ComparisonResults, aiAnalysis: AIAnalysisResponse): string {
  const { totalMasterClauses, matchedClauses, missingClauses, modifiedClauses, additionalClauses, complianceRate } = comparisonResults

  return `MEL Compliance Summary:
• Compliance Rate: ${complianceRate}%
• Matched: ${matchedClauses}/${totalMasterClauses} clauses
• Issues: ${missingClauses} missing, ${modifiedClauses} modified, ${additionalClauses} additional
• Assessment: ${aiAnalysis.complianceAssessment}
• Recommendation: ${aiAnalysis.inspectorRecommendation}
• Critical Issues: ${aiAnalysis.criticalIssues.length}
• Warnings: ${aiAnalysis.warnings.length}`
}
