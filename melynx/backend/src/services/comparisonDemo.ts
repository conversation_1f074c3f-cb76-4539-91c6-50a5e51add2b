#!/usr/bin/env tsx

/**
 * MEL Comparison and AI Analysis Demo
 * Demonstrates the clause comparison and AI analysis capabilities
 */

import { compareMELClauses } from './melComparator.js'
import { generateComplianceAnalysis } from './aiSummarizer.js'
import { MELClause, IssueSeverity } from '../../../shared/src/types/index.js'

// Sample test data
const masterClauses: MELClause[] = [
  {
    id: 'master_27_10_01',
    clauseNumber: '27-10-01',
    title: 'Aileron Tab',
    content: 'May be inoperative provided flight is conducted in accordance with approved procedures',
    category: 'C',
    section: '27-10',
    conditions: ['VFR flight only'],
    limitations: ['Maximum crosswind 15 knots'],
    maintenanceActions: ['Repair within 10 flight hours'],
    placard: 'AILERON TAB INOP'
  },
  {
    id: 'master_32_41_01',
    clauseNumber: '32-41-01',
    title: 'Main Landing Gear Door',
    content: 'May be inoperative provided door is secured in closed position',
    category: 'A',
    section: '32-41',
    conditions: ['Day VFR only'],
    maintenanceActions: ['Repair before next flight']
  },
  {
    id: 'master_34_11_01',
    clauseNumber: '34-11-01',
    title: 'Navigation Light',
    content: 'May be inoperative provided alternate lighting is available',
    category: 'B',
    section: '34-11',
    conditions: ['Night flight prohibited'],
    limitations: ['VFR only']
  }
]

const operatorClauses: MELClause[] = [
  {
    id: 'operator_27_10_01',
    clauseNumber: '27-10-01',
    title: 'Aileron Tab',
    content: 'May be inoperative provided flight is conducted in accordance with approved procedures',
    category: 'C',
    section: '27-10',
    conditions: ['VFR flight only'],
    limitations: ['Maximum crosswind 15 knots'],
    maintenanceActions: ['Repair within 10 flight hours'],
    placard: 'AILERON TAB INOP'
  },
  {
    id: 'operator_32_41_01',
    clauseNumber: '32-41-01',
    title: 'Main Landing Gear Door',
    content: 'May be inoperative provided door is secured and no leakage evident',
    category: 'B', // Changed from A to B - CRITICAL ISSUE
    section: '32-41',
    conditions: ['Day VFR only', 'No hydraulic leakage'],
    maintenanceActions: ['Repair within 24 hours'] // Changed from 'before next flight'
  },
  {
    id: 'operator_35_12_01',
    clauseNumber: '35-12-01',
    title: 'Oxygen System',
    content: 'May be inoperative for flights below 10,000 feet',
    category: 'C',
    section: '35-12',
    conditions: ['Flight below 10,000 feet'],
    limitations: ['Maximum altitude 10,000 feet']
  }
  // Note: Missing 34-11-01 Navigation Light clause
]

async function runComparisonDemo() {
  console.log('🚀 MELynx Clause Comparison & AI Analysis Demo\n')
  
  try {
    // Step 1: Perform clause comparison
    console.log('📋 Step 1: Performing Clause Comparison...')
    const comparisonResults = await compareMELClauses(operatorClauses, masterClauses, {
      strictCategoryMatching: true,
      checkMaintenanceActions: true,
      checkOperationalProcedures: true,
      checkConditionsAndLimitations: true
    })
    
    console.log(`✅ Comparison completed in ${Date.now()}ms`)
    console.log(`📊 Results Summary:`)
    console.log(`   • Total Master Clauses: ${comparisonResults.totalMasterClauses}`)
    console.log(`   • Total Operator Clauses: ${comparisonResults.totalOperatorClauses}`)
    console.log(`   • Matched: ${comparisonResults.matchedClauses}`)
    console.log(`   • Missing: ${comparisonResults.missingClauses}`)
    console.log(`   • Modified: ${comparisonResults.modifiedClauses}`)
    console.log(`   • Additional: ${comparisonResults.additionalClauses}`)
    console.log(`   • Conflicting: ${comparisonResults.conflictingClauses}`)
    console.log(`   • Compliance Rate: ${comparisonResults.complianceRate}%\n`)
    
    // Step 2: Display detailed differences
    console.log('🔍 Step 2: Detailed Differences Analysis...')
    comparisonResults.differences.forEach((diff, index) => {
      console.log(`\n${index + 1}. ${diff.clauseNumber} - ${diff.differenceType.toUpperCase()}`)
      console.log(`   Type: ${diff.differenceType.toUpperCase()}`)
      console.log(`   Severity: ${diff.severity}`)
      console.log(`   Differences: ${diff.differences.join('; ')}`)
      console.log(`   Recommendation: ${diff.recommendation}`)
    })
    
    // Step 3: AI Analysis (if API key is available)
    console.log('\n🤖 Step 3: AI Compliance Analysis...')
    
    if (process.env.OPENROUTER_API_KEY) {
      try {
        const aiAnalysis = await generateComplianceAnalysis({
          differences: comparisonResults.differences,
          comparisonResults,
          aircraftType: 'Boeing 737-800',
          operatorName: 'Demo Airlines',
          inspectorName: 'Demo Inspector',
          analysisOptions: {
            includeRecommendations: true,
            focusOnCriticalIssues: true,
            includeRegulatoryReferences: true
          }
        })
        
        console.log(`✅ AI Analysis completed in ${aiAnalysis.processingTime}ms`)
        console.log(`📋 AI Assessment: ${aiAnalysis.complianceAssessment}`)
        console.log(`👨‍✈️ Inspector Recommendation: ${aiAnalysis.inspectorRecommendation}`)
        console.log(`🎯 Confidence: ${aiAnalysis.confidence}%`)
        console.log(`\n📝 Summary:`)
        console.log(`${aiAnalysis.summary}`)
        
        if (aiAnalysis.criticalIssues.length > 0) {
          console.log(`\n🚨 Critical Issues (${aiAnalysis.criticalIssues.length}):`)
          aiAnalysis.criticalIssues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue.title}`)
            console.log(`   Description: ${issue.description}`)
            console.log(`   Recommendation: ${issue.recommendation}`)
            if (issue.regulatoryReference) {
              console.log(`   Regulation: ${issue.regulatoryReference}`)
            }
          })
        }
        
        if (aiAnalysis.warnings.length > 0) {
          console.log(`\n⚠️  Warnings (${aiAnalysis.warnings.length}):`)
          aiAnalysis.warnings.forEach((warning, index) => {
            console.log(`${index + 1}. ${warning.title}`)
            console.log(`   Description: ${warning.description}`)
          })
        }
        
        if (aiAnalysis.recommendations.length > 0) {
          console.log(`\n💡 Recommendations:`)
          aiAnalysis.recommendations.forEach((rec, index) => {
            console.log(`${index + 1}. ${rec}`)
          })
        }
        
      } catch (aiError) {
        console.log(`❌ AI Analysis failed: ${aiError instanceof Error ? aiError.message : 'Unknown error'}`)
        console.log(`   This is expected if OpenRouter API key is not configured`)
      }
    } else {
      console.log(`⚠️  OpenRouter API key not configured - skipping AI analysis`)
      console.log(`   Set OPENROUTER_API_KEY environment variable to enable AI features`)
    }
    
    // Step 4: Summary and next steps
    console.log('\n📋 Analysis Summary:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    
    const criticalCount = comparisonResults.differences.filter(d => d.severity === IssueSeverity.CRITICAL).length
    const highCount = comparisonResults.differences.filter(d => d.severity === IssueSeverity.HIGH).length
    
    if (criticalCount > 0) {
      console.log(`🚨 CRITICAL: ${criticalCount} critical issues require immediate attention`)
    }
    if (highCount > 0) {
      console.log(`⚠️  HIGH: ${highCount} high-priority issues need review`)
    }
    
    console.log(`📊 Overall Compliance: ${comparisonResults.complianceRate}%`)
    
    if (comparisonResults.complianceRate >= 95) {
      console.log(`✅ RECOMMENDATION: APPROVE - High compliance rate`)
    } else if (comparisonResults.complianceRate >= 85) {
      console.log(`⚠️  RECOMMENDATION: CONDITIONAL APPROVAL - Address identified issues`)
    } else {
      console.log(`❌ RECOMMENDATION: REJECT - Significant compliance gaps`)
    }
    
    console.log('\n🎉 Demo completed successfully!')
    
  } catch (error) {
    console.error('❌ Demo failed:', error)
    process.exit(1)
  }
}

// Display sample clause structure
function displaySampleClauseStructure() {
  console.log('\n📋 Sample MEL Clause Structure:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log(JSON.stringify({
    id: "master_32_41_01",
    clauseNumber: "32-41-01",
    title: "Main Landing Gear Door",
    content: "May be inoperative provided door is secured in closed position",
    category: "A",
    section: "32-41",
    conditions: ["Day VFR only"],
    limitations: [],
    maintenanceActions: ["Repair before next flight"],
    operationalProcedures: [],
    placard: null,
    remarks: null,
    page: 28
  }, null, 2))
}

// Run demo if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runComparisonDemo()
    .then(() => {
      displaySampleClauseStructure()
      process.exit(0)
    })
    .catch((error) => {
      console.error('Demo failed:', error)
      process.exit(1)
    })
}

export { runComparisonDemo, displaySampleClauseStructure }
