#!/usr/bin/env tsx

/**
 * MEL Document Parsing Demo
 * Demonstrates the parsing capabilities for different file formats
 */

import { parseDocument } from './documentParser.js'
import { 
  createTestBuffers, 
  validateParsingResults, 
  EXPECTED_PARSING_RESULTS,
  SAMPLE_MEL_TEXT,
  SAMPLE_XML_MEL,
  SAMPLE_JSON_MEL
} from './testData.js'

async function runParsingDemo() {
  console.log('🚀 MELynx Document Parsing Demo\n')
  
  const testBuffers = createTestBuffers()
  
  // Test 1: Plain Text Parsing
  console.log('📄 Testing Plain Text Parsing...')
  try {
    const textResult = await parseDocument(
      testBuffers.text,
      'text/plain',
      'sample_mel.txt',
      {
        extractMetadata: true,
        validateClauses: true,
        normalizeContent: true
      }
    )
    
    console.log(`✅ Extracted ${textResult.clauses.length} clauses from text`)
    console.log(`📊 Metadata:`, textResult.metadata)
    console.log(`🔍 First clause:`, {
      number: textResult.clauses[0]?.clauseNumber,
      title: textResult.clauses[0]?.title,
      category: textResult.clauses[0]?.category,
      section: textResult.clauses[0]?.section
    })
    
    const isValid = validateParsingResults(textResult, EXPECTED_PARSING_RESULTS.textParsing)
    console.log(`✨ Validation: ${isValid ? 'PASSED' : 'FAILED'}\n`)
    
  } catch (error) {
    console.error('❌ Text parsing failed:', error)
  }
  
  // Test 2: XML Parsing
  console.log('📋 Testing XML Parsing...')
  try {
    const xmlResult = await parseDocument(
      testBuffers.xml,
      'application/xml',
      'sample_mel.xml',
      {
        extractMetadata: true,
        validateClauses: true,
        normalizeContent: true
      }
    )
    
    console.log(`✅ Extracted ${xmlResult.clauses.length} clauses from XML`)
    console.log(`📊 Metadata:`, xmlResult.metadata)
    console.log(`🔍 First clause:`, {
      number: xmlResult.clauses[0]?.clauseNumber,
      title: xmlResult.clauses[0]?.title,
      category: xmlResult.clauses[0]?.category,
      conditions: xmlResult.clauses[0]?.conditions
    })
    
    const isValid = validateParsingResults(xmlResult, EXPECTED_PARSING_RESULTS.xmlParsing)
    console.log(`✨ Validation: ${isValid ? 'PASSED' : 'FAILED'}\n`)
    
  } catch (error) {
    console.error('❌ XML parsing failed:', error)
  }
  
  // Test 3: JSON Parsing
  console.log('📦 Testing JSON Parsing...')
  try {
    const jsonResult = await parseDocument(
      testBuffers.json,
      'application/json',
      'sample_mel.json',
      {
        extractMetadata: true,
        validateClauses: true,
        normalizeContent: true
      }
    )
    
    console.log(`✅ Extracted ${jsonResult.clauses.length} clauses from JSON`)
    console.log(`📊 Metadata:`, jsonResult.metadata)
    console.log(`🔍 First clause:`, {
      number: jsonResult.clauses[0]?.clauseNumber,
      title: jsonResult.clauses[0]?.title,
      category: jsonResult.clauses[0]?.category,
      maintenanceActions: jsonResult.clauses[0]?.maintenanceActions
    })
    
    const isValid = validateParsingResults(jsonResult, EXPECTED_PARSING_RESULTS.jsonParsing)
    console.log(`✨ Validation: ${isValid ? 'PASSED' : 'FAILED'}\n`)
    
  } catch (error) {
    console.error('❌ JSON parsing failed:', error)
  }
  
  // Test 4: Error Handling
  console.log('⚠️  Testing Error Handling...')
  try {
    const invalidBuffer = Buffer.from('This is not a valid MEL document', 'utf-8')
    await parseDocument(
      invalidBuffer,
      'application/pdf',
      'invalid.pdf'
    )
  } catch (error) {
    console.log(`✅ Error handling works: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
  
  console.log('\n🎉 Parsing demo completed!')
}

// Example MEL clause output
function displaySampleOutput() {
  console.log('\n📋 Sample MEL Clause Structure:')
  console.log(JSON.stringify({
    id: "clause_27_10_01_1234567890",
    clauseNumber: "27-10-01",
    title: "Aileron Tab",
    content: "May be inoperative provided flight is conducted in accordance with approved procedures",
    category: "C",
    section: "27-10",
    conditions: ["VFR flight only", "Flight crew briefed"],
    limitations: ["Maximum crosswind 15 knots"],
    maintenanceActions: ["Repair within 10 flight hours"],
    operationalProcedures: [],
    placard: "AILERON TAB INOP",
    remarks: undefined,
    page: undefined
  }, null, 2))
}

// Run demo if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runParsingDemo()
    .then(() => {
      displaySampleOutput()
      process.exit(0)
    })
    .catch((error) => {
      console.error('Demo failed:', error)
      process.exit(1)
    })
}

export { runParsingDemo, displaySampleOutput }
