import { supabase, supabaseAdmin } from '../utils/supabase.js'
import { MELDocument, DocumentMetadata, DocumentStatus } from '../../../shared/src/types/index.js'
import { logger } from '../utils/logger.js'
import crypto from 'crypto'
import path from 'path'

/**
 * Storage Service for MEL documents
 * Handles file upload to Supabase storage and metadata management
 */

export interface UploadResult {
  document: MELDocument
  storageUrl: string
}

export interface StorageOptions {
  bucket?: string
  folder?: string
  generateThumbnail?: boolean
}

const DEFAULT_BUCKET = 'melynx-documents'
const DEFAULT_FOLDER = 'mel-files'

/**
 * Upload file to Supabase storage and save metadata to database
 */
export async function uploadDocument(
  file: Express.Multer.File,
  metadata: Partial<DocumentMetadata>,
  uploadedBy: string,
  options: StorageOptions = {}
): Promise<UploadResult> {
  const startTime = Date.now()
  
  try {
    logger.info(`Starting document upload: ${file.originalname} (${file.size} bytes)`)
    
    // Generate unique filename
    const fileExtension = path.extname(file.originalname)
    const timestamp = Date.now()
    const hash = crypto.createHash('md5').update(file.buffer).digest('hex').substring(0, 8)
    const filename = `${timestamp}_${hash}${fileExtension}`
    
    // Construct storage path
    const bucket = options.bucket || DEFAULT_BUCKET
    const folder = options.folder || DEFAULT_FOLDER
    const storagePath = `${folder}/${filename}`
    
    // Upload file to Supabase storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(storagePath, file.buffer, {
        contentType: file.mimetype,
        duplex: 'half'
      })
    
    if (uploadError) {
      logger.error('Supabase storage upload failed:', uploadError)
      throw new Error(`Storage upload failed: ${uploadError.message}`)
    }
    
    logger.debug(`File uploaded to storage: ${storagePath}`)
    
    // Get public URL for the uploaded file
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(storagePath)
    
    const storageUrl = urlData.publicUrl
    
    // Create document metadata for database
    const documentData = {
      filename,
      original_name: file.originalname,
      mime_type: file.mimetype,
      size: file.size,
      storage_path: storagePath,
      uploaded_by: uploadedBy,
      status: DocumentStatus.UPLOADED,
      
      // Document metadata
      aircraft_type: metadata.aircraftType,
      operator: metadata.operator,
      effective_date: metadata.effectiveDate,
      revision_number: metadata.revisionNumber,
      document_type: metadata.documentType || 'other',
      page_count: metadata.pageCount,
      language: metadata.language || 'en'
    }
    
    // Insert document metadata into database
    const { data: dbData, error: dbError } = await supabase
      .from('mel_documents')
      .insert(documentData)
      .select()
      .single()
    
    if (dbError) {
      // If database insert fails, clean up uploaded file
      await cleanupStorageFile(bucket, storagePath)
      logger.error('Database insert failed:', dbError)
      throw new Error(`Database insert failed: ${dbError.message}`)
    }
    
    // Convert database record to MELDocument interface
    const document: MELDocument = {
      id: dbData.id,
      filename: dbData.filename,
      originalName: dbData.original_name,
      mimeType: dbData.mime_type,
      size: dbData.size,
      uploadedAt: dbData.uploaded_at,
      parsedAt: dbData.parsed_at,
      status: dbData.status as DocumentStatus,
      metadata: {
        aircraftType: dbData.aircraft_type,
        operator: dbData.operator,
        effectiveDate: dbData.effective_date,
        revisionNumber: dbData.revision_number,
        documentType: dbData.document_type as 'operator_mel' | 'master_mel' | 'other',
        pageCount: dbData.page_count,
        language: dbData.language
      },
      clauses: [] // Will be populated during parsing
    }
    
    const processingTime = Date.now() - startTime
    logger.info(`Document upload completed: ${filename} in ${processingTime}ms`)
    
    return {
      document,
      storageUrl
    }
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`Document upload failed after ${processingTime}ms:`, error)
    throw error
  }
}

/**
 * Update document status in database
 */
export async function updateDocumentStatus(
  documentId: string,
  status: DocumentStatus,
  parsedAt?: string
): Promise<void> {
  try {
    const updateData: any = { status, updated_at: new Date().toISOString() }
    
    if (parsedAt) {
      updateData.parsed_at = parsedAt
    }
    
    const { error } = await supabase
      .from('mel_documents')
      .update(updateData)
      .eq('id', documentId)
    
    if (error) {
      logger.error(`Failed to update document status for ${documentId}:`, error)
      throw new Error(`Status update failed: ${error.message}`)
    }
    
    logger.debug(`Document status updated: ${documentId} -> ${status}`)
    
  } catch (error) {
    logger.error('Document status update error:', error)
    throw error
  }
}

/**
 * Get document by ID
 */
export async function getDocument(documentId: string): Promise<MELDocument | null> {
  try {
    const { data, error } = await supabase
      .from('mel_documents')
      .select('*')
      .eq('id', documentId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null // Document not found
      }
      throw new Error(`Database query failed: ${error.message}`)
    }
    
    // Convert to MELDocument interface
    const document: MELDocument = {
      id: data.id,
      filename: data.filename,
      originalName: data.original_name,
      mimeType: data.mime_type,
      size: data.size,
      uploadedAt: data.uploaded_at,
      parsedAt: data.parsed_at,
      status: data.status as DocumentStatus,
      metadata: {
        aircraftType: data.aircraft_type,
        operator: data.operator,
        effectiveDate: data.effective_date,
        revisionNumber: data.revision_number,
        documentType: data.document_type as 'operator_mel' | 'master_mel' | 'other',
        pageCount: data.page_count,
        language: data.language
      },
      clauses: [] // Clauses loaded separately
    }
    
    return document
    
  } catch (error) {
    logger.error(`Failed to get document ${documentId}:`, error)
    throw error
  }
}

/**
 * Download file from Supabase storage
 */
export async function downloadDocument(storagePath: string, bucket: string = DEFAULT_BUCKET): Promise<Buffer> {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .download(storagePath)
    
    if (error) {
      logger.error(`Failed to download file ${storagePath}:`, error)
      throw new Error(`Download failed: ${error.message}`)
    }
    
    const buffer = Buffer.from(await data.arrayBuffer())
    logger.debug(`Downloaded file: ${storagePath} (${buffer.length} bytes)`)
    
    return buffer
    
  } catch (error) {
    logger.error('File download error:', error)
    throw error
  }
}

/**
 * Delete document and associated file
 */
export async function deleteDocument(documentId: string): Promise<void> {
  try {
    // Get document info first
    const document = await getDocument(documentId)
    if (!document) {
      throw new Error('Document not found')
    }
    
    // Delete from database first
    const { error: dbError } = await supabase
      .from('mel_documents')
      .delete()
      .eq('id', documentId)
    
    if (dbError) {
      throw new Error(`Database deletion failed: ${dbError.message}`)
    }
    
    // Delete from storage
    const storagePath = `${DEFAULT_FOLDER}/${document.filename}`
    await cleanupStorageFile(DEFAULT_BUCKET, storagePath)
    
    logger.info(`Document deleted: ${documentId}`)
    
  } catch (error) {
    logger.error(`Failed to delete document ${documentId}:`, error)
    throw error
  }
}

/**
 * Clean up storage file (helper function)
 */
async function cleanupStorageFile(bucket: string, storagePath: string): Promise<void> {
  try {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([storagePath])
    
    if (error) {
      logger.warn(`Failed to clean up storage file ${storagePath}:`, error)
    } else {
      logger.debug(`Cleaned up storage file: ${storagePath}`)
    }
  } catch (error) {
    logger.warn('Storage cleanup error:', error)
  }
}

/**
 * List documents for a user
 */
export async function listDocuments(
  uploadedBy?: string,
  documentType?: string,
  limit: number = 50,
  offset: number = 0
): Promise<MELDocument[]> {
  try {
    let query = supabase
      .from('mel_documents')
      .select('*')
      .order('uploaded_at', { ascending: false })
      .range(offset, offset + limit - 1)
    
    if (uploadedBy) {
      query = query.eq('uploaded_by', uploadedBy)
    }
    
    if (documentType) {
      query = query.eq('document_type', documentType)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(`Database query failed: ${error.message}`)
    }
    
    return data.map(item => ({
      id: item.id,
      filename: item.filename,
      originalName: item.original_name,
      mimeType: item.mime_type,
      size: item.size,
      uploadedAt: item.uploaded_at,
      parsedAt: item.parsed_at,
      status: item.status as DocumentStatus,
      metadata: {
        aircraftType: item.aircraft_type,
        operator: item.operator,
        effectiveDate: item.effective_date,
        revisionNumber: item.revision_number,
        documentType: item.document_type as 'operator_mel' | 'master_mel' | 'other',
        pageCount: item.page_count,
        language: item.language
      },
      clauses: []
    }))
    
  } catch (error) {
    logger.error('Failed to list documents:', error)
    throw error
  }
}
