import { supabase } from '../utils/supabase.js'
import { MELClause } from '../../../shared/src/types/index.js'
import { logger } from '../utils/logger.js'

/**
 * Clause Service for MEL clause operations
 * Handles saving, retrieving, and managing MEL clauses in the database
 */

/**
 * Save extracted clauses to database
 */
export async function saveClauses(documentId: string, clauses: MELClause[]): Promise<void> {
  const startTime = Date.now()
  
  try {
    logger.info(`Saving ${clauses.length} clauses for document ${documentId}`)
    
    // Prepare clause data for database insertion
    const clauseData = clauses.map(clause => ({
      document_id: documentId,
      clause_number: clause.clauseNumber,
      title: clause.title,
      content: clause.content,
      category: clause.category,
      subcategory: clause.subcategory,
      conditions: clause.conditions || [],
      limitations: clause.limitations || [],
      maintenance_actions: clause.maintenanceActions || [],
      operational_procedures: clause.operationalProcedures || [],
      placard: clause.placard,
      remarks: clause.remarks,
      page_number: clause.page,
      section: clause.section
    }))
    
    // Insert clauses in batches to avoid query size limits
    const batchSize = 100
    const batches = []
    
    for (let i = 0; i < clauseData.length; i += batchSize) {
      batches.push(clauseData.slice(i, i + batchSize))
    }
    
    for (const batch of batches) {
      const { error } = await supabase
        .from('mel_clauses')
        .insert(batch)
      
      if (error) {
        logger.error(`Failed to insert clause batch:`, error)
        throw new Error(`Clause insertion failed: ${error.message}`)
      }
    }
    
    const processingTime = Date.now() - startTime
    logger.info(`Successfully saved ${clauses.length} clauses in ${processingTime}ms`)
    
  } catch (error) {
    const processingTime = Date.now() - startTime
    logger.error(`Failed to save clauses after ${processingTime}ms:`, error)
    throw error
  }
}

/**
 * Get clauses for a document
 */
export async function getClauses(documentId: string): Promise<MELClause[]> {
  try {
    const { data, error } = await supabase
      .from('mel_clauses')
      .select('*')
      .eq('document_id', documentId)
      .order('clause_number')
    
    if (error) {
      logger.error(`Failed to get clauses for document ${documentId}:`, error)
      throw new Error(`Database query failed: ${error.message}`)
    }
    
    // Convert database records to MELClause interface
    const clauses: MELClause[] = data.map(item => ({
      id: item.id,
      clauseNumber: item.clause_number,
      title: item.title,
      content: item.content,
      category: item.category,
      subcategory: item.subcategory,
      conditions: item.conditions || [],
      limitations: item.limitations || [],
      maintenanceActions: item.maintenance_actions || [],
      operationalProcedures: item.operational_procedures || [],
      placard: item.placard,
      remarks: item.remarks,
      page: item.page_number,
      section: item.section
    }))
    
    logger.debug(`Retrieved ${clauses.length} clauses for document ${documentId}`)
    return clauses
    
  } catch (error) {
    logger.error('Failed to get clauses:', error)
    throw error
  }
}

/**
 * Get clause by ID
 */
export async function getClause(clauseId: string): Promise<MELClause | null> {
  try {
    const { data, error } = await supabase
      .from('mel_clauses')
      .select('*')
      .eq('id', clauseId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null // Clause not found
      }
      throw new Error(`Database query failed: ${error.message}`)
    }
    
    const clause: MELClause = {
      id: data.id,
      clauseNumber: data.clause_number,
      title: data.title,
      content: data.content,
      category: data.category,
      subcategory: data.subcategory,
      conditions: data.conditions || [],
      limitations: data.limitations || [],
      maintenanceActions: data.maintenance_actions || [],
      operationalProcedures: data.operational_procedures || [],
      placard: data.placard,
      remarks: data.remarks,
      page: data.page_number,
      section: data.section
    }
    
    return clause
    
  } catch (error) {
    logger.error(`Failed to get clause ${clauseId}:`, error)
    throw error
  }
}

/**
 * Search clauses by various criteria
 */
export async function searchClauses(
  documentId?: string,
  clauseNumber?: string,
  category?: string,
  section?: string,
  searchText?: string,
  limit: number = 50,
  offset: number = 0
): Promise<MELClause[]> {
  try {
    let query = supabase
      .from('mel_clauses')
      .select('*')
      .order('clause_number')
      .range(offset, offset + limit - 1)
    
    if (documentId) {
      query = query.eq('document_id', documentId)
    }
    
    if (clauseNumber) {
      query = query.eq('clause_number', clauseNumber)
    }
    
    if (category) {
      query = query.eq('category', category)
    }
    
    if (section) {
      query = query.eq('section', section)
    }
    
    if (searchText) {
      query = query.or(`title.ilike.%${searchText}%,content.ilike.%${searchText}%`)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(`Database query failed: ${error.message}`)
    }
    
    const clauses: MELClause[] = data.map(item => ({
      id: item.id,
      clauseNumber: item.clause_number,
      title: item.title,
      content: item.content,
      category: item.category,
      subcategory: item.subcategory,
      conditions: item.conditions || [],
      limitations: item.limitations || [],
      maintenanceActions: item.maintenance_actions || [],
      operationalProcedures: item.operational_procedures || [],
      placard: item.placard,
      remarks: item.remarks,
      page: item.page_number,
      section: item.section
    }))
    
    logger.debug(`Found ${clauses.length} clauses matching search criteria`)
    return clauses
    
  } catch (error) {
    logger.error('Failed to search clauses:', error)
    throw error
  }
}

/**
 * Update clause
 */
export async function updateClause(clauseId: string, updates: Partial<MELClause>): Promise<void> {
  try {
    const updateData: any = {}
    
    if (updates.title) updateData.title = updates.title
    if (updates.content) updateData.content = updates.content
    if (updates.category) updateData.category = updates.category
    if (updates.subcategory) updateData.subcategory = updates.subcategory
    if (updates.conditions) updateData.conditions = updates.conditions
    if (updates.limitations) updateData.limitations = updates.limitations
    if (updates.maintenanceActions) updateData.maintenance_actions = updates.maintenanceActions
    if (updates.operationalProcedures) updateData.operational_procedures = updates.operationalProcedures
    if (updates.placard) updateData.placard = updates.placard
    if (updates.remarks) updateData.remarks = updates.remarks
    if (updates.page) updateData.page_number = updates.page
    if (updates.section) updateData.section = updates.section
    
    updateData.updated_at = new Date().toISOString()
    
    const { error } = await supabase
      .from('mel_clauses')
      .update(updateData)
      .eq('id', clauseId)
    
    if (error) {
      logger.error(`Failed to update clause ${clauseId}:`, error)
      throw new Error(`Clause update failed: ${error.message}`)
    }
    
    logger.debug(`Clause updated: ${clauseId}`)
    
  } catch (error) {
    logger.error('Failed to update clause:', error)
    throw error
  }
}

/**
 * Delete clauses for a document (used when re-parsing)
 */
export async function deleteClauses(documentId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('mel_clauses')
      .delete()
      .eq('document_id', documentId)
    
    if (error) {
      logger.error(`Failed to delete clauses for document ${documentId}:`, error)
      throw new Error(`Clause deletion failed: ${error.message}`)
    }
    
    logger.debug(`Deleted clauses for document: ${documentId}`)
    
  } catch (error) {
    logger.error('Failed to delete clauses:', error)
    throw error
  }
}

/**
 * Get clause statistics for a document
 */
export async function getClauseStats(documentId: string): Promise<{
  totalClauses: number
  categoryCounts: Record<string, number>
  sectionCounts: Record<string, number>
}> {
  try {
    const clauses = await getClauses(documentId)
    
    const stats = {
      totalClauses: clauses.length,
      categoryCounts: {} as Record<string, number>,
      sectionCounts: {} as Record<string, number>
    }
    
    clauses.forEach(clause => {
      // Count by category
      const category = clause.category || 'Unknown'
      stats.categoryCounts[category] = (stats.categoryCounts[category] || 0) + 1
      
      // Count by section
      const section = clause.section || 'Unknown'
      stats.sectionCounts[section] = (stats.sectionCounts[section] || 0) + 1
    })
    
    return stats
    
  } catch (error) {
    logger.error(`Failed to get clause stats for document ${documentId}:`, error)
    throw error
  }
}
