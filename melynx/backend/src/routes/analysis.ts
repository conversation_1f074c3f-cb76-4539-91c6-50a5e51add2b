import { Router } from 'express'
import { logger } from '../utils/logger.js'
import { startAnalysis, getAnalysisStatus, getAnalysisResults, cancelAnalysis, listAnalyses } from '../services/analysisService.js'
import { AnalysisStatus } from '@shared/types/index.js'

const router = Router()

/**
 * Start MEL analysis
 * POST /api/analysis/start
 * Body: { operatorMelId, masterMelId, createdBy, options? }
 */
router.post('/start', async (req, res) => {
  try {
    const { operatorMelId, masterMelId, createdBy, options } = req.body

    // Validate required fields
    if (!operatorMelId || !masterMelId || !createdBy) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'operatorMelId, masterMelId, and createdBy are required'
      })
    }

    logger.info(`Starting MEL analysis: operator=${operatorMelId}, master=${masterMelId}, user=${createdBy}`)

    // Start the analysis
    const analysis = await startAnalysis({
      operatorMelId,
      masterMelId,
      createdBy,
      options
    })

    res.json({
      message: 'MEL analysis started successfully',
      analysis: {
        id: analysis.id,
        status: analysis.status,
        operatorMelId: analysis.operatorMelId,
        masterMelId: analysis.masterMelId,
        startedAt: analysis.startedAt,
        options: analysis.options
      },
      estimatedTime: '3-5 minutes'
    })

  } catch (error) {
    logger.error('Analysis start error:', error)
    res.status(500).json({
      error: 'Analysis failed to start',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Get analysis status
 * GET /api/analysis/status/:analysisId
 */
router.get('/status/:analysisId', async (req, res) => {
  try {
    const { analysisId } = req.params

    if (!analysisId) {
      return res.status(400).json({
        error: 'Missing analysis ID',
        message: 'Analysis ID is required'
      })
    }

    const status = await getAnalysisStatus(analysisId)

    res.json({
      analysisId: status.analysisId,
      status: status.status,
      progress: status.progress,
      currentStage: status.currentStage,
      estimatedTimeRemaining: status.estimatedTimeRemaining,
      error: status.error
    })

  } catch (error) {
    logger.error('Analysis status error:', error)

    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Analysis not found',
        message: error.message
      })
    }

    res.status(500).json({
      error: 'Status check failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Get analysis results
 * GET /api/analysis/results/:analysisId
 */
router.get('/results/:analysisId', async (req, res) => {
  try {
    const { analysisId } = req.params

    if (!analysisId) {
      return res.status(400).json({
        error: 'Missing analysis ID',
        message: 'Analysis ID is required'
      })
    }

    const results = await getAnalysisResults(analysisId)

    res.json({
      analysisId,
      status: 'completed',
      results: {
        complianceRate: results.complianceRate,
        totalClauses: results.totalClauses,
        matchedClauses: results.matchedClauses,
        missingClauses: results.missingClauses,
        modifiedClauses: results.modifiedClauses,
        additionalClauses: results.additionalClauses,
        criticalIssues: results.criticalIssues,
        warnings: results.warnings,
        recommendations: results.recommendations,
        summary: results.summary,
        clauseComparisons: results.clauseComparisons
      }
    })

  } catch (error) {
    logger.error('Results retrieval error:', error)

    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Analysis not found',
        message: error.message
      })
    }

    if (error instanceof Error && error.message.includes('not completed')) {
      return res.status(400).json({
        error: 'Analysis not completed',
        message: error.message
      })
    }

    res.status(500).json({
      error: 'Results retrieval failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Get all analyses for user
 * GET /api/analysis/list?createdBy=xxx&status=xxx&page=1&limit=20
 */
router.get('/list', async (req, res) => {
  try {
    const {
      createdBy,
      status,
      page = '1',
      limit = '20'
    } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const offset = (pageNum - 1) * limitNum

    const analyses = await listAnalyses(
      createdBy as string,
      status as AnalysisStatus,
      limitNum,
      offset
    )

    res.json({
      analyses,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: analyses.length,
        hasMore: analyses.length === limitNum
      }
    })

  } catch (error) {
    logger.error('Analysis listing error:', error)
    res.status(500).json({
      error: 'Listing failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Cancel analysis
 * POST /api/analysis/cancel/:analysisId
 */
router.post('/cancel/:analysisId', async (req, res) => {
  try {
    const { analysisId } = req.params

    if (!analysisId) {
      return res.status(400).json({
        error: 'Missing analysis ID',
        message: 'Analysis ID is required'
      })
    }

    await cancelAnalysis(analysisId)

    res.json({
      message: 'Analysis cancelled successfully',
      analysisId,
      status: 'cancelled'
    })

  } catch (error) {
    logger.error('Analysis cancellation error:', error)

    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Analysis not found',
        message: error.message
      })
    }

    res.status(500).json({
      error: 'Cancellation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

export default router
