import { Router } from 'express';

const router = Router();

// Get evaluation by ID
router.get('/evaluations/:id', (req, res) => {
  const { id } = req.params;

  // Mock evaluation data
  const mockEvaluation = {
    id,
    filename: 'sample-mel-document.pdf',
    status: 'completed',
    created_at: new Date().toISOString(),
    completed_at: new Date().toISOString(),
    page_count: 25,
    processed_pages: 25,
    analysis: {
      compliance_score: 85,
      total_items: 50,
      compliant_items: 42,
      non_compliant_items: 5,
      warnings: 3,
      critical_issues: 2,
      findings: [
        {
          type: 'critical',
          title: 'Missing MEL Reference',
          description: 'Critical system component lacks proper MEL reference documentation.',
          page_number: 12,
          recommendation: 'Add complete MEL reference with proper categorization.'
        },
        {
          type: 'warning',
          title: 'Incomplete Maintenance Procedure',
          description: 'Maintenance procedure description is incomplete.',
          page_number: 18,
          recommendation: 'Complete the maintenance procedure with detailed steps.'
        }
      ]
    }
  };

  res.json(mockEvaluation);
});

// Get all evaluations
router.get('/evaluations', (req, res) => {
  const mockEvaluations = [
    {
      id: 'eval-1',
      filename: 'boeing-737-mel.pdf',
      status: 'completed',
      created_at: new Date(Date.now() - 86400000).toISOString(),
      compliance_score: 92,
      critical_issues: 1,
      warnings: 2,
      page_count: 45,
      processed_pages: 45
    },
    {
      id: 'eval-2',
      filename: 'airbus-a320-mel.pdf',
      status: 'completed',
      created_at: new Date(Date.now() - 172800000).toISOString(),
      compliance_score: 78,
      critical_issues: 3,
      warnings: 5,
      page_count: 38,
      processed_pages: 38
    }
  ];

  res.json(mockEvaluations);
});

export default router;