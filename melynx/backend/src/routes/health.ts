import { Router } from 'express'
import { supabase } from '../utils/supabase.js'
import { logger } from '../utils/logger.js'

const router = Router()

/**
 * Health check endpoint
 * GET /api/health
 */
router.get('/', async (req, res) => {
  try {
    const startTime = Date.now()
    
    // Check database connectivity
    const { data, error } = await supabase
      .from('mel_evaluations')
      .select('count')
      .limit(1)
    
    const dbStatus = error ? 'error' : 'healthy'
    const responseTime = Date.now() - startTime
    
    // Check OpenRouter API (basic connectivity)
    const openRouterStatus = process.env.OPENROUTER_API_KEY ? 'configured' : 'not_configured'
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: {
          status: dbStatus,
          responseTime: `${responseTime}ms`
        },
        openrouter: {
          status: openRouterStatus
        },
        storage: {
          status: 'healthy' // Supabase storage check could be added here
        }
      },
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    }
    
    // If any critical service is down, return 503
    if (dbStatus === 'error') {
      return res.status(503).json({
        ...healthData,
        status: 'unhealthy',
        error: 'Database connectivity issues'
      })
    }
    
    res.json(healthData)
    
  } catch (error) {
    logger.error('Health check failed:', error)
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

/**
 * Detailed system status
 * GET /api/health/detailed
 */
router.get('/detailed', async (req, res) => {
  try {
    // This would include more comprehensive checks
    // - Database table counts
    // - Storage usage
    // - API rate limits
    // - Recent error rates
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'Detailed health check - implementation pending'
    })
    
  } catch (error) {
    logger.error('Detailed health check failed:', error)
    res.status(503).json({
      status: 'unhealthy',
      error: 'Detailed health check failed'
    })
  }
})

export default router
