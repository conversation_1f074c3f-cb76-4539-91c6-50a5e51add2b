import { Router } from 'express'
import multer from 'multer'
import { logger } from '../utils/logger.js'

const router = Router()

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['application/pdf', 'application/msword', 
                         'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                         'text/plain', 'application/xml', 'text/xml']
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('Invalid file type. Only PDF, DOC, DOCX, TXT, and XML files are allowed.'))
    }
  }
})

/**
 * Upload MEL documents
 * POST /api/upload
 */
router.post('/', upload.array('files', 10), async (req, res) => {
  try {
    const files = req.files as Express.Multer.File[]
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select at least one MEL document to upload'
      })
    }
    
    logger.info(`Received ${files.length} files for upload`)
    
    // Process each file
    const processedFiles = files.map(file => ({
      originalName: file.originalname,
      size: file.size,
      mimeType: file.mimetype,
      uploadedAt: new Date().toISOString(),
      // In the next phase, we'll add:
      // - File parsing
      // - Storage to Supabase
      // - MEL clause extraction
      status: 'uploaded'
    }))
    
    // TODO: Implement in Power Prompt 3
    // - Parse document content
    // - Extract MEL clauses
    // - Store in Supabase storage
    // - Save metadata to database
    
    res.json({
      message: 'Files uploaded successfully',
      files: processedFiles,
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      nextStep: 'Document parsing and MEL extraction will be implemented in Power Prompt 3'
    })
    
  } catch (error) {
    logger.error('File upload error:', error)
    
    if (error instanceof multer.MulterError) {
      if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          error: 'File too large',
          message: 'Maximum file size is 50MB'
        })
      }
      if (error.code === 'LIMIT_FILE_COUNT') {
        return res.status(400).json({
          error: 'Too many files',
          message: 'Maximum 10 files allowed per upload'
        })
      }
    }
    
    res.status(500).json({
      error: 'Upload failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Get upload status
 * GET /api/upload/status/:uploadId
 */
router.get('/status/:uploadId', async (req, res) => {
  try {
    const { uploadId } = req.params
    
    // TODO: Implement upload status tracking
    // - Query database for upload status
    // - Return parsing progress
    // - Return any errors
    
    res.json({
      uploadId,
      status: 'pending',
      message: 'Upload status tracking will be implemented in Power Prompt 3'
    })
    
  } catch (error) {
    logger.error('Status check error:', error)
    res.status(500).json({
      error: 'Status check failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Delete uploaded file
 * DELETE /api/upload/:fileId
 */
router.delete('/:fileId', async (req, res) => {
  try {
    const { fileId } = req.params
    
    // TODO: Implement file deletion
    // - Remove from Supabase storage
    // - Remove metadata from database
    // - Clean up any related analysis data
    
    res.json({
      message: 'File deletion will be implemented in Power Prompt 3',
      fileId
    })
    
  } catch (error) {
    logger.error('File deletion error:', error)
    res.status(500).json({
      error: 'Deletion failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

export default router
