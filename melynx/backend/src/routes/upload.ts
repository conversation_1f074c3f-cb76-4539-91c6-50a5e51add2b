import { Router } from 'express'
import multer from 'multer'
import { logger } from '../utils/logger.js'
import { parseDocument } from '../services/documentParser.js'
import { uploadDocument, updateDocumentStatus, getDocument, deleteDocument, downloadDocument, listDocuments } from '../services/storageService.js'
import { saveClauses, getClauses, getClauseStats } from '../services/clauseService.js'
import { DocumentStatus, DocumentMetadata } from '../../../shared/src/types/index.js'
import { FILE_UPLOAD } from '../../../shared/src/constants/index.js'

const router = Router()

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: FILE_UPLOAD.MAX_SIZE,
    files: FILE_UPLOAD.MAX_FILES
  },
  fileFilter: (req, file, cb) => {
    if (FILE_UPLOAD.ALLOWED_TYPES.includes(file.mimetype as any)) {
      cb(null, true)
    } else {
      cb(new Error(`Invalid file type. Only ${FILE_UPLOAD.ALLOWED_EXTENSIONS.join(', ')} files are allowed.`))
    }
  }
})

/**
 * Upload MEL documents
 * POST /api/upload
 * Body: { aircraftType, operator, documentType } (form data)
 * Files: MEL documents (multipart/form-data)
 */
router.post('/', upload.array('files', FILE_UPLOAD.MAX_FILES), async (req, res) => {
  try {
    const files = req.files as Express.Multer.File[]
    const { aircraftType, operator, documentType, inspectorId } = req.body

    // Validate input
    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select at least one MEL document to upload'
      })
    }

    if (!inspectorId) {
      return res.status(400).json({
        error: 'Missing inspector ID',
        message: 'Inspector ID is required for file uploads'
      })
    }

    logger.info(`Processing ${files.length} files for inspector ${inspectorId}`)

    const uploadResults = []
    const errors = []

    // Process each file
    for (const file of files) {
      try {
        logger.info(`Processing file: ${file.originalname}`)

        // Prepare metadata
        const metadata: Partial<DocumentMetadata> = {
          aircraftType,
          operator,
          documentType: documentType || 'other'
        }

        // Upload file to storage and save metadata
        const { document, storageUrl } = await uploadDocument(file, metadata, inspectorId)

        // Update status to parsing
        await updateDocumentStatus(document.id, DocumentStatus.PARSING)

        try {
          // Parse document content
          logger.info(`Parsing document: ${document.id}`)
          const parseResult = await parseDocument(
            file.buffer,
            file.mimetype,
            file.originalname,
            {
              extractMetadata: true,
              validateClauses: true,
              normalizeContent: true
            }
          )

          // Update document metadata with parsed information
          if (parseResult.metadata.aircraftType && !metadata.aircraftType) {
            metadata.aircraftType = parseResult.metadata.aircraftType
          }
          if (parseResult.metadata.operator && !metadata.operator) {
            metadata.operator = parseResult.metadata.operator
          }
          if (parseResult.metadata.documentType && documentType === 'other') {
            metadata.documentType = parseResult.metadata.documentType
          }

          // Save extracted clauses
          if (parseResult.clauses.length > 0) {
            await saveClauses(document.id, parseResult.clauses)
            logger.info(`Saved ${parseResult.clauses.length} clauses for document ${document.id}`)
          }

          // Update status to parsed
          await updateDocumentStatus(document.id, DocumentStatus.PARSED, new Date().toISOString())

          // Get clause statistics
          const stats = await getClauseStats(document.id)

          uploadResults.push({
            document: {
              ...document,
              metadata: { ...document.metadata, ...metadata },
              status: DocumentStatus.PARSED
            },
            storageUrl,
            parsing: {
              success: true,
              clausesExtracted: parseResult.clauses.length,
              pageCount: parseResult.pageCount,
              stats
            }
          })

        } catch (parseError) {
          logger.error(`Parsing failed for document ${document.id}:`, parseError)

          // Update status to failed
          await updateDocumentStatus(document.id, DocumentStatus.FAILED)

          uploadResults.push({
            document: {
              ...document,
              status: DocumentStatus.FAILED
            },
            storageUrl,
            parsing: {
              success: false,
              error: parseError instanceof Error ? parseError.message : 'Unknown parsing error'
            }
          })
        }

      } catch (fileError) {
        logger.error(`Failed to process file ${file.originalname}:`, fileError)
        errors.push({
          filename: file.originalname,
          error: fileError instanceof Error ? fileError.message : 'Unknown error'
        })
      }
    }

    const successCount = uploadResults.filter(r => r.parsing.success).length
    const failureCount = uploadResults.length - successCount + errors.length

    logger.info(`Upload completed: ${successCount} successful, ${failureCount} failed`)

    res.json({
      message: `Processed ${files.length} files: ${successCount} successful, ${failureCount} failed`,
      results: uploadResults,
      errors,
      summary: {
        totalFiles: files.length,
        successful: successCount,
        failed: failureCount,
        totalSize: files.reduce((sum, file) => sum + file.size, 0)
      }
    })

  } catch (error) {
    logger.error('Upload processing error:', error)

    if (error instanceof multer.MulterError) {
      if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          error: 'File too large',
          message: `Maximum file size is ${FILE_UPLOAD.MAX_SIZE / 1024 / 1024}MB`
        })
      }
      if (error.code === 'LIMIT_FILE_COUNT') {
        return res.status(400).json({
          error: 'Too many files',
          message: `Maximum ${FILE_UPLOAD.MAX_FILES} files allowed per upload`
        })
      }
    }

    res.status(500).json({
      error: 'Upload processing failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Get document status and details
 * GET /api/upload/status/:documentId
 */
router.get('/status/:documentId', async (req, res) => {
  try {
    const { documentId } = req.params

    // Get document details
    const document = await getDocument(documentId)

    if (!document) {
      return res.status(404).json({
        error: 'Document not found',
        message: `Document with ID ${documentId} does not exist`
      })
    }

    // Get clauses if document is parsed
    let clauses: any[] = []
    let stats = null

    if (document.status === DocumentStatus.PARSED) {
      clauses = await getClauses(documentId)
      stats = await getClauseStats(documentId)
    }

    res.json({
      document,
      clauses: clauses.length > 0 ? clauses : undefined,
      stats,
      message: `Document status: ${document.status}`
    })

  } catch (error) {
    logger.error('Status check error:', error)
    res.status(500).json({
      error: 'Status check failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Delete uploaded document
 * DELETE /api/upload/:documentId
 */
router.delete('/:documentId', async (req, res) => {
  try {
    const { documentId } = req.params

    // Check if document exists
    const document = await getDocument(documentId)
    if (!document) {
      return res.status(404).json({
        error: 'Document not found',
        message: `Document with ID ${documentId} does not exist`
      })
    }

    logger.info(`Deleting document: ${documentId} (${document.originalName})`)

    // Delete document and associated files
    await deleteDocument(documentId)

    res.json({
      message: 'Document deleted successfully',
      documentId,
      filename: document.originalName
    })

  } catch (error) {
    logger.error('File deletion error:', error)
    res.status(500).json({
      error: 'Deletion failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Download document file
 * GET /api/upload/download/:documentId
 */
router.get('/download/:documentId', async (req, res) => {
  try {
    const { documentId } = req.params

    // Get document details
    const document = await getDocument(documentId)
    if (!document) {
      return res.status(404).json({
        error: 'Document not found',
        message: `Document with ID ${documentId} does not exist`
      })
    }

    // Download file from storage
    const storagePath = `mel-files/${document.filename}`
    const fileBuffer = await downloadDocument(storagePath)

    // Set appropriate headers
    res.setHeader('Content-Type', document.mimeType)
    res.setHeader('Content-Disposition', `attachment; filename="${document.originalName}"`)
    res.setHeader('Content-Length', fileBuffer.length)

    res.send(fileBuffer)

  } catch (error) {
    logger.error('File download error:', error)
    res.status(500).json({
      error: 'Download failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * List documents for inspector
 * GET /api/upload/list?inspectorId=xxx&documentType=xxx&limit=20&offset=0
 */
router.get('/list', async (req, res) => {
  try {
    const { inspectorId, documentType, limit = '20', offset = '0' } = req.query

    if (!inspectorId) {
      return res.status(400).json({
        error: 'Missing inspector ID',
        message: 'Inspector ID is required to list documents'
      })
    }

    const documents = await listDocuments(
      inspectorId as string,
      documentType as string,
      parseInt(limit as string),
      parseInt(offset as string)
    )

    res.json({
      documents,
      pagination: {
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        total: documents.length
      }
    })

  } catch (error) {
    logger.error('Document listing error:', error)
    res.status(500).json({
      error: 'Listing failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

export default router
