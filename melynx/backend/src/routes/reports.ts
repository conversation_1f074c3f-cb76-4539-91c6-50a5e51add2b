import { Router } from 'express'
import { logger } from '../utils/logger.js'
import { generatePDFReport, generateJ<PERSON>NReport, saveReportToStorage } from '../services/reportGenerator.js'
import { supabase } from '../utils/supabase.js'
import { AnalysisStatus } from '../../../shared/src/types/index.js'

const router = Router()

/**
 * Generate and download PDF report
 * GET /api/reports/:analysisId/pdf
 */
router.get('/:analysisId/pdf', async (req, res) => {
  try {
    const { analysisId } = req.params
    const { 
      inspectorName, 
      inspectorLicense, 
      includeDetailedFindings = 'true',
      includeSignatureBlock = 'true'
    } = req.query
    
    if (!analysisId) {
      return res.status(400).json({
        error: 'Missing analysis ID',
        message: 'Analysis ID is required'
      })
    }
    
    logger.info(`Generating PDF report for analysis ${analysisId}`)
    
    // Check if analysis exists and is completed
    const { data: analysisData, error: analysisError } = await supabase
      .from('mel_evaluations')
      .select('status, operator_mel_id, master_mel_id')
      .eq('id', analysisId)
      .single()
    
    if (analysisError || !analysisData) {
      return res.status(404).json({
        error: 'Analysis not found',
        message: `Analysis with ID ${analysisId} does not exist`
      })
    }
    
    if (analysisData.status !== AnalysisStatus.COMPLETED) {
      return res.status(400).json({
        error: 'Analysis not completed',
        message: `Analysis status is ${analysisData.status}. Reports can only be generated for completed analyses.`
      })
    }
    
    // Generate PDF report
    const pdfBuffer = await generatePDFReport(analysisId, {
      inspectorName: inspectorName as string,
      inspectorLicense: inspectorLicense as string,
      includeDetailedFindings: includeDetailedFindings === 'true',
      includeSignatureBlock: includeSignatureBlock === 'true'
    })
    
    // Save to storage
    const storagePath = await saveReportToStorage(analysisId, pdfBuffer, 'pdf')
    
    // Set response headers
    const filename = `MEL_Compliance_Report_${analysisId}.pdf`
    res.setHeader('Content-Type', 'application/pdf')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Length', pdfBuffer.length)
    res.setHeader('X-Storage-Path', storagePath)
    
    // Send PDF
    res.send(pdfBuffer)
    
    logger.info(`PDF report generated and sent: ${filename} (${pdfBuffer.length} bytes)`)
    
  } catch (error) {
    logger.error('PDF report generation error:', error)
    res.status(500).json({
      error: 'Report generation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Generate and download JSON report
 * GET /api/reports/:analysisId/json
 */
router.get('/:analysisId/json', async (req, res) => {
  try {
    const { analysisId } = req.params
    const { 
      inspectorName, 
      inspectorLicense, 
      includeDetailedFindings = 'true',
      includeAuditTrail = 'false'
    } = req.query
    
    if (!analysisId) {
      return res.status(400).json({
        error: 'Missing analysis ID',
        message: 'Analysis ID is required'
      })
    }
    
    logger.info(`Generating JSON report for analysis ${analysisId}`)
    
    // Check if analysis exists and is completed
    const { data: analysisData, error: analysisError } = await supabase
      .from('mel_evaluations')
      .select('status')
      .eq('id', analysisId)
      .single()
    
    if (analysisError || !analysisData) {
      return res.status(404).json({
        error: 'Analysis not found',
        message: `Analysis with ID ${analysisId} does not exist`
      })
    }
    
    if (analysisData.status !== AnalysisStatus.COMPLETED) {
      return res.status(400).json({
        error: 'Analysis not completed',
        message: `Analysis status is ${analysisData.status}. Reports can only be generated for completed analyses.`
      })
    }
    
    // Generate JSON report
    const jsonReport = await generateJSONReport(analysisId, {
      inspectorName: inspectorName as string,
      inspectorLicense: inspectorLicense as string,
      includeDetailedFindings: includeDetailedFindings === 'true',
      includeAuditTrail: includeAuditTrail === 'true'
    })
    
    // Save to storage
    const jsonBuffer = Buffer.from(JSON.stringify(jsonReport, null, 2))
    const storagePath = await saveReportToStorage(analysisId, jsonBuffer, 'json')
    
    // Set response headers
    const filename = `MEL_Compliance_Report_${analysisId}.json`
    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('X-Storage-Path', storagePath)
    
    // Send JSON
    res.json(jsonReport)
    
    logger.info(`JSON report generated and sent: ${filename}`)
    
  } catch (error) {
    logger.error('JSON report generation error:', error)
    res.status(500).json({
      error: 'Report generation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Approve analysis
 * POST /api/reports/:analysisId/approve
 */
router.post('/:analysisId/approve', async (req, res) => {
  try {
    const { analysisId } = req.params
    const { inspectorId, comments, conditions } = req.body
    
    if (!analysisId || !inspectorId) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Analysis ID and inspector ID are required'
      })
    }
    
    logger.info(`Approving analysis ${analysisId} by inspector ${inspectorId}`)
    
    // Update analysis status
    const { error: updateError } = await supabase
      .from('mel_evaluations')
      .update({
        status: 'approved',
        approved_by: inspectorId,
        approved_at: new Date().toISOString(),
        approval_comments: comments,
        approval_conditions: conditions,
        updated_at: new Date().toISOString()
      })
      .eq('id', analysisId)
    
    if (updateError) {
      throw new Error(`Failed to update analysis: ${updateError.message}`)
    }
    
    // Log audit trail
    await logAuditAction(analysisId, inspectorId, 'APPROVED', comments)
    
    res.json({
      message: 'Analysis approved successfully',
      analysisId,
      approvedBy: inspectorId,
      approvedAt: new Date().toISOString(),
      comments,
      conditions
    })
    
  } catch (error) {
    logger.error('Analysis approval error:', error)
    res.status(500).json({
      error: 'Approval failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Reject analysis (request revision)
 * POST /api/reports/:analysisId/reject
 */
router.post('/:analysisId/reject', async (req, res) => {
  try {
    const { analysisId } = req.params
    const { inspectorId, comments, requiredChanges } = req.body
    
    if (!analysisId || !inspectorId) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Analysis ID and inspector ID are required'
      })
    }
    
    logger.info(`Rejecting analysis ${analysisId} by inspector ${inspectorId}`)
    
    // Update analysis status
    const { error: updateError } = await supabase
      .from('mel_evaluations')
      .update({
        status: 'rejected',
        rejected_by: inspectorId,
        rejected_at: new Date().toISOString(),
        rejection_comments: comments,
        required_changes: requiredChanges,
        updated_at: new Date().toISOString()
      })
      .eq('id', analysisId)
    
    if (updateError) {
      throw new Error(`Failed to update analysis: ${updateError.message}`)
    }
    
    // Log audit trail
    await logAuditAction(analysisId, inspectorId, 'REJECTED', comments)
    
    res.json({
      message: 'Analysis rejected - revision required',
      analysisId,
      rejectedBy: inspectorId,
      rejectedAt: new Date().toISOString(),
      comments,
      requiredChanges
    })
    
  } catch (error) {
    logger.error('Analysis rejection error:', error)
    res.status(500).json({
      error: 'Rejection failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Get analysis audit trail
 * GET /api/reports/:analysisId/audit
 */
router.get('/:analysisId/audit', async (req, res) => {
  try {
    const { analysisId } = req.params
    
    const { data: auditData, error: auditError } = await supabase
      .from('mel_audit_log')
      .select('*')
      .eq('analysis_id', analysisId)
      .order('created_at', { ascending: false })
    
    if (auditError) {
      throw new Error(`Failed to get audit trail: ${auditError.message}`)
    }
    
    res.json({
      analysisId,
      auditTrail: auditData || []
    })
    
  } catch (error) {
    logger.error('Audit trail retrieval error:', error)
    res.status(500).json({
      error: 'Audit trail retrieval failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    })
  }
})

/**
 * Helper function to log audit actions
 */
async function logAuditAction(
  analysisId: string, 
  inspectorId: string, 
  action: string, 
  comments?: string
): Promise<void> {
  try {
    const { error } = await supabase
      .from('mel_audit_log')
      .insert({
        analysis_id: analysisId,
        inspector_id: inspectorId,
        action,
        comments,
        created_at: new Date().toISOString()
      })
    
    if (error) {
      logger.error('Failed to log audit action:', error)
    }
  } catch (error) {
    logger.error('Audit logging error:', error)
  }
}

export default router
