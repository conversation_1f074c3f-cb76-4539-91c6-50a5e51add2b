import { describe, it, expect, beforeEach } from 'vitest'
import { compareMELClauses, MELDifference } from '../services/melComparator.js'
import { MELClause, ComparisonStatus, IssueSeverity } from '../../../shared/src/types/index.js'

/**
 * Test cases for MEL Comparator Service
 */

describe('MEL Comparator Service', () => {
  let masterClauses: MELClause[]
  let operatorClauses: MELClause[]

  beforeEach(() => {
    // Sample Master MEL clauses
    masterClauses = [
      {
        id: 'master_27_10_01',
        clauseNumber: '27-10-01',
        title: 'Aileron Tab',
        content: 'May be inoperative provided flight is conducted in accordance with approved procedures',
        category: 'C',
        section: '27-10',
        conditions: ['VFR flight only'],
        limitations: ['Maximum crosswind 15 knots'],
        maintenanceActions: ['Repair within 10 flight hours'],
        placard: 'AILERON TAB INOP'
      },
      {
        id: 'master_32_41_01',
        clauseNumber: '32-41-01',
        title: 'Main Landing Gear Door',
        content: 'May be inoperative provided door is secured in closed position',
        category: 'A',
        section: '32-41',
        conditions: ['Day VFR only'],
        maintenanceActions: ['Repair before next flight']
      },
      {
        id: 'master_34_11_01',
        clauseNumber: '34-11-01',
        title: 'Navigation Light',
        content: 'May be inoperative provided alternate lighting is available',
        category: 'B',
        section: '34-11',
        conditions: ['Night flight prohibited'],
        limitations: ['VFR only']
      }
    ]

    // Sample Operator MEL clauses (with various differences)
    operatorClauses = [
      {
        id: 'operator_27_10_01',
        clauseNumber: '27-10-01',
        title: 'Aileron Tab',
        content: 'May be inoperative provided flight is conducted in accordance with approved procedures',
        category: 'C',
        section: '27-10',
        conditions: ['VFR flight only'],
        limitations: ['Maximum crosswind 15 knots'],
        maintenanceActions: ['Repair within 10 flight hours'],
        placard: 'AILERON TAB INOP'
      },
      {
        id: 'operator_32_41_01',
        clauseNumber: '32-41-01',
        title: 'Main Landing Gear Door',
        content: 'May be inoperative provided door is secured and no leakage evident',
        category: 'B', // Changed from A to B
        section: '32-41',
        conditions: ['Day VFR only', 'No hydraulic leakage'], // Added condition
        maintenanceActions: ['Repair within 24 hours'] // Changed from 'before next flight'
      },
      {
        id: 'operator_35_12_01',
        clauseNumber: '35-12-01',
        title: 'Oxygen System',
        content: 'May be inoperative for flights below 10,000 feet',
        category: 'C',
        section: '35-12',
        conditions: ['Flight below 10,000 feet'],
        limitations: ['Maximum altitude 10,000 feet']
      }
      // Note: Missing 34-11-01 from operator MEL
    ]
  })

  describe('Basic Comparison', () => {
    it('should identify matched clauses correctly', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      expect(results.matchedClauses).toBe(1) // Only 27-10-01 matches exactly
      expect(results.totalOperatorClauses).toBe(3)
      expect(results.totalMasterClauses).toBe(3)
    })

    it('should identify missing clauses', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      expect(results.missingClauses).toBe(1) // 34-11-01 is missing
      
      const missingDiff = results.differences.find(d => d.differenceType === 'missing')
      expect(missingDiff).toBeDefined()
      expect(missingDiff?.clauseNumber).toBe('34-11-01')
      expect(missingDiff?.severity).toBe(IssueSeverity.HIGH)
    })

    it('should identify modified clauses', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      expect(results.modifiedClauses).toBe(1) // 32-41-01 is modified
      
      const modifiedDiff = results.differences.find(d => d.differenceType === 'modified')
      expect(modifiedDiff).toBeDefined()
      expect(modifiedDiff?.clauseNumber).toBe('32-41-01')
      expect(modifiedDiff?.differences.length).toBeGreaterThan(0)
    })

    it('should identify additional clauses', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      expect(results.additionalClauses).toBe(1) // 35-12-01 is additional
      
      const additionalDiff = results.differences.find(d => d.differenceType === 'additional')
      expect(additionalDiff).toBeDefined()
      expect(additionalDiff?.clauseNumber).toBe('35-12-01')
      expect(additionalDiff?.severity).toBe(IssueSeverity.MEDIUM)
    })

    it('should calculate compliance rate correctly', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      // 1 matched out of 3 master clauses = 33.33%
      expect(results.complianceRate).toBeCloseTo(33.33, 1)
    })
  })

  describe('Detailed Difference Detection', () => {
    it('should detect category differences', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      const modifiedComparison = results.clauseComparisons.find(
        c => c.operatorClause.clauseNumber === '32-41-01'
      )
      
      expect(modifiedComparison).toBeDefined()
      expect(modifiedComparison?.differences.some(d => d.includes('Category differs'))).toBe(true)
      expect(modifiedComparison?.severity).toBe(IssueSeverity.HIGH)
    })

    it('should detect content differences', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      const modifiedComparison = results.clauseComparisons.find(
        c => c.operatorClause.clauseNumber === '32-41-01'
      )
      
      expect(modifiedComparison?.differences.some(d => d.includes('differs'))).toBe(true)
    })

    it('should detect maintenance action differences', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses)
      
      const modifiedComparison = results.clauseComparisons.find(
        c => c.operatorClause.clauseNumber === '32-41-01'
      )
      
      expect(modifiedComparison?.differences.some(d => d.includes('maintenance'))).toBe(true)
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty clause arrays', async () => {
      const results = await compareMELClauses([], [])
      
      expect(results.totalOperatorClauses).toBe(0)
      expect(results.totalMasterClauses).toBe(0)
      expect(results.complianceRate).toBe(100)
      expect(results.differences).toHaveLength(0)
    })

    it('should handle operator MEL with no master clauses', async () => {
      const results = await compareMELClauses(operatorClauses, [])
      
      expect(results.additionalClauses).toBe(operatorClauses.length)
      expect(results.differences).toHaveLength(operatorClauses.length)
      expect(results.differences.every(d => d.differenceType === 'additional')).toBe(true)
    })

    it('should handle master MEL with no operator clauses', async () => {
      const results = await compareMELClauses([], masterClauses)
      
      expect(results.missingClauses).toBe(masterClauses.length)
      expect(results.differences).toHaveLength(masterClauses.length)
      expect(results.differences.every(d => d.differenceType === 'missing')).toBe(true)
    })
  })

  describe('Comparison Options', () => {
    it('should respect strict category matching option', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses, {
        strictCategoryMatching: true
      })
      
      const modifiedComparison = results.clauseComparisons.find(
        c => c.operatorClause.clauseNumber === '32-41-01'
      )
      
      expect(modifiedComparison?.status).toBe(ComparisonStatus.CONFLICTING)
    })

    it('should skip maintenance action checks when disabled', async () => {
      const results = await compareMELClauses(operatorClauses, masterClauses, {
        checkMaintenanceActions: false
      })
      
      const modifiedComparison = results.clauseComparisons.find(
        c => c.operatorClause.clauseNumber === '32-41-01'
      )
      
      // Should have fewer differences when maintenance actions are not checked
      expect(modifiedComparison?.differences.some(d => d.includes('maintenance'))).toBe(false)
    })
  })

  describe('Performance', () => {
    it('should handle large clause sets efficiently', async () => {
      // Generate large clause sets
      const largeMasterClauses = Array.from({ length: 1000 }, (_, i) => ({
        id: `master_${i}`,
        clauseNumber: `${Math.floor(i / 100) + 20}-${Math.floor((i % 100) / 10)}-${i % 10}`,
        title: `Test Clause ${i}`,
        content: `Test content for clause ${i}`,
        category: ['A', 'B', 'C', 'D'][i % 4],
        section: `${Math.floor(i / 100) + 20}-${Math.floor((i % 100) / 10)}`
      }))
      
      const largeOperatorClauses = largeMasterClauses.slice(0, 800) // 80% match
      
      const startTime = Date.now()
      const results = await compareMELClauses(largeOperatorClauses, largeMasterClauses)
      const endTime = Date.now()
      
      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
      expect(results.matchedClauses).toBe(800)
      expect(results.missingClauses).toBe(200)
    })
  })
})
