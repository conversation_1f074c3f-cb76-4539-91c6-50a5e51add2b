import { createClient } from '@supabase/supabase-js'
import { logger } from './logger.js'

const supabaseUrl = process.env.SUPABASE_URL
const supabaseKey = process.env.SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  logger.error('Missing Supabase configuration')
  throw new Error('SUPABASE_URL and SUPABASE_ANON_KEY must be provided')
}

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true
  }
})

// Service role client for admin operations
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

export const supabaseAdmin = supabaseServiceKey 
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null

if (!supabaseAdmin) {
  logger.warn('Supabase service role key not provided - admin operations will be limited')
}

// Test connection
supabase
  .from('mel_evaluations')
  .select('count')
  .limit(1)
  .then(({ error }) => {
    if (error) {
      logger.error('Supabase connection test failed:', error.message)
    } else {
      logger.info('Supabase connection established successfully')
    }
  })
  .catch((error) => {
    logger.error('Supabase connection error:', error)
  })
