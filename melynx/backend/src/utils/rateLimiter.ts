import { RateLimiterMemory } from 'rate-limiter-flexible'
import { Request, Response, NextFunction } from 'express'
import { logger } from './logger.js'

// Rate limiter configuration
const rateLimiterOptions = {
  points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // Number of requests
  duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000') / 1000, // Per 15 minutes (in seconds)
  blockDuration: 60, // Block for 1 minute if limit exceeded
}

const rateLimiter = new RateLimiterMemory(rateLimiterOptions)

// Stricter rate limiting for upload endpoints
const uploadRateLimiter = new RateLimiterMemory({
  points: 10, // 10 uploads
  duration: 900, // Per 15 minutes
  blockDuration: 300, // Block for 5 minutes
})

// Even stricter for analysis endpoints
const analysisRateLimiter = new RateLimiterMemory({
  points: 5, // 5 analyses
  duration: 3600, // Per hour
  blockDuration: 600, // Block for 10 minutes
})

export const rateLimiterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Choose appropriate rate limiter based on endpoint
    let limiter = rateLimiter
    
    if (req.path.startsWith('/api/upload')) {
      limiter = uploadRateLimiter
    } else if (req.path.startsWith('/api/analysis')) {
      limiter = analysisRateLimiter
    }
    
    await limiter.consume(req.ip || 'unknown')
    next()
  } catch (rejRes: any) {
    const remainingPoints = rejRes?.remainingPoints || 0
    const msBeforeNext = rejRes?.msBeforeNext || 0
    const totalHits = rejRes?.totalHits || 0
    
    logger.warn('Rate limit exceeded:', {
      ip: req.ip || 'unknown',
      path: req.path,
      method: req.method,
      remainingPoints,
      msBeforeNext,
      totalHits,
      userAgent: req.get('User-Agent')
    })
    
    res.set({
      'Retry-After': Math.round(msBeforeNext / 1000) || 1,
      'X-RateLimit-Limit': rateLimiterOptions.points,
      'X-RateLimit-Remaining': remainingPoints,
      'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
    })
    
    res.status(429).json({
      error: 'Too Many Requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.round(msBeforeNext / 1000),
      limit: rateLimiterOptions.points,
      remaining: remainingPoints
    })
  }
}

export { rateLimiterMiddleware as rateLimiter }
