{"name": "melynx", "version": "1.0.0", "description": "MELynx - AI-Powered MEL Compliance Inspector for Aviation", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "npm run dev --workspace=frontend", "build": "npm run build --workspace=frontend && npm run build --workspace=backend", "start": "npm run start --workspace=backend"}, "keywords": ["aviation", "mel", "compliance", "ai"], "author": "MELynx Team", "license": "MIT"}