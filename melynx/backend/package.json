{"name": "melynx-backend", "version": "1.0.0", "description": "MELynx Backend - AI-Powered MEL Compliance Inspector", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^6.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "tesseract.js": "^4.1.1", "axios": "^1.3.4", "@supabase/supabase-js": "^2.39.7", "winston": "^3.8.2", "joi": "^17.8.3", "rate-limiter-flexible": "^2.4.1", "dotenv": "^16.0.3"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^18.15.3", "typescript": "^5.0.2", "tsx": "^3.12.3", "jest": "^29.5.0", "@types/jest": "^29.5.0", "eslint": "^8.36.0", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1"}, "keywords": ["aviation", "mel", "compliance", "ai", "backend"], "author": "MELynx Team", "license": "MIT"}