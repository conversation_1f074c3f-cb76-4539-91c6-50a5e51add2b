{"name": "@melynx/backend", "version": "1.0.0", "description": "MELynx Backend API and Services", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src --ext .ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext .ts --fix", "db:generate": "supabase gen types typescript --local > src/types/database.types.ts", "db:migrate": "supabase migration up", "db:reset": "supabase db reset"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "@supabase/supabase-js": "^2.38.4", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "xml2js": "^0.6.2", "axios": "^1.6.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "rate-limiter-flexible": "^4.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/xml2js": "^0.4.14", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "tsx": "^4.6.0", "typescript": "^5.2.2", "vitest": "^0.34.6", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}}