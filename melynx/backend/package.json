{"name": "melynx-backend", "version": "1.0.0", "description": "MELynx Backend API", "type": "module", "main": "minimal-server.js", "scripts": {"start": "node minimal-server.js", "dev": "node minimal-server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "tesseract.js": "^5.0.4", "pdf2pic": "^3.1.1", "sharp": "^0.33.2", "dotenv": "^16.3.1"}, "keywords": ["aviation", "mel", "compliance"], "author": "MELynx Team", "license": "MIT"}