#!/usr/bin/env node

/**
 * Simple demo runner for MEL comparison without tsx dependency
 */

import { compareMELClauses } from './dist/services/melComparator.js'

// Sample test data
const masterClauses = [
  {
    id: 'master_27_10_01',
    clauseNumber: '27-10-01',
    title: 'Aileron Tab',
    content: 'May be inoperative provided flight is conducted in accordance with approved procedures',
    category: 'C',
    section: '27-10',
    conditions: ['VFR flight only'],
    limitations: ['Maximum crosswind 15 knots'],
    maintenanceActions: ['Repair within 10 flight hours'],
    placard: 'AILERON TAB INOP'
  },
  {
    id: 'master_32_41_01',
    clauseNumber: '32-41-01',
    title: 'Main Landing Gear Door',
    content: 'May be inoperative provided door is secured in closed position',
    category: 'A',
    section: '32-41',
    conditions: ['Day VFR only'],
    maintenanceActions: ['Repair before next flight']
  },
  {
    id: 'master_34_11_01',
    clauseNumber: '34-11-01',
    title: 'Navigation Light',
    content: 'May be inoperative provided alternate lighting is available',
    category: 'B',
    section: '34-11',
    conditions: ['Night flight prohibited'],
    limitations: ['VFR only']
  }
]

const operatorClauses = [
  {
    id: 'operator_27_10_01',
    clauseNumber: '27-10-01',
    title: 'Aileron Tab',
    content: 'May be inoperative provided flight is conducted in accordance with approved procedures',
    category: 'C',
    section: '27-10',
    conditions: ['VFR flight only'],
    limitations: ['Maximum crosswind 15 knots'],
    maintenanceActions: ['Repair within 10 flight hours'],
    placard: 'AILERON TAB INOP'
  },
  {
    id: 'operator_32_41_01',
    clauseNumber: '32-41-01',
    title: 'Main Landing Gear Door',
    content: 'May be inoperative provided door is secured and no leakage evident',
    category: 'B', // Changed from A to B - CRITICAL ISSUE
    section: '32-41',
    conditions: ['Day VFR only', 'No hydraulic leakage'],
    maintenanceActions: ['Repair within 24 hours'] // Changed from 'before next flight'
  },
  {
    id: 'operator_35_12_01',
    clauseNumber: '35-12-01',
    title: 'Oxygen System',
    content: 'May be inoperative for flights below 10,000 feet',
    category: 'C',
    section: '35-12',
    conditions: ['Flight below 10,000 feet'],
    limitations: ['Maximum altitude 10,000 feet']
  }
  // Note: Missing 34-11-01 Navigation Light clause
]

async function runDemo() {
  console.log('🚀 MELynx Clause Comparison Demo\n')
  
  try {
    console.log('📋 Performing Clause Comparison...')
    const startTime = Date.now()
    
    const comparisonResults = await compareMELClauses(operatorClauses, masterClauses, {
      strictCategoryMatching: true,
      checkMaintenanceActions: true,
      checkOperationalProcedures: true,
      checkConditionsAndLimitations: true
    })
    
    const endTime = Date.now()
    console.log(`✅ Comparison completed in ${endTime - startTime}ms\n`)
    
    console.log('📊 Results Summary:')
    console.log(`   • Total Master Clauses: ${comparisonResults.totalMasterClauses}`)
    console.log(`   • Total Operator Clauses: ${comparisonResults.totalOperatorClauses}`)
    console.log(`   • Matched: ${comparisonResults.matchedClauses}`)
    console.log(`   • Missing: ${comparisonResults.missingClauses}`)
    console.log(`   • Modified: ${comparisonResults.modifiedClauses}`)
    console.log(`   • Additional: ${comparisonResults.additionalClauses}`)
    console.log(`   • Conflicting: ${comparisonResults.conflictingClauses}`)
    console.log(`   • Compliance Rate: ${comparisonResults.complianceRate}%\n`)
    
    console.log('🔍 Detailed Differences Analysis:')
    comparisonResults.differences.forEach((diff, index) => {
      console.log(`\n${index + 1}. ${diff.clauseNumber} - ${diff.differenceType.toUpperCase()}`)
      console.log(`   Severity: ${diff.severity}`)
      console.log(`   Differences: ${diff.differences.join('; ')}`)
      console.log(`   Recommendation: ${diff.recommendation}`)
    })
    
    console.log('\n📋 Analysis Summary:')
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
    
    const criticalCount = comparisonResults.differences.filter(d => d.severity === 'CRITICAL').length
    const highCount = comparisonResults.differences.filter(d => d.severity === 'HIGH').length
    
    if (criticalCount > 0) {
      console.log(`🚨 CRITICAL: ${criticalCount} critical issues require immediate attention`)
    }
    if (highCount > 0) {
      console.log(`⚠️  HIGH: ${highCount} high-priority issues need review`)
    }
    
    console.log(`📊 Overall Compliance: ${comparisonResults.complianceRate}%`)
    
    if (comparisonResults.complianceRate >= 95) {
      console.log(`✅ RECOMMENDATION: APPROVE - High compliance rate`)
    } else if (comparisonResults.complianceRate >= 85) {
      console.log(`⚠️  RECOMMENDATION: CONDITIONAL APPROVAL - Address identified issues`)
    } else {
      console.log(`❌ RECOMMENDATION: REJECT - Significant compliance gaps`)
    }
    
    console.log('\n🎉 Demo completed successfully!')
    console.log('\n💡 Note: AI analysis requires OpenRouter API key configuration')
    console.log('   Set OPENROUTER_API_KEY environment variable to enable AI features')
    
  } catch (error) {
    console.error('❌ Demo failed:', error.message)
    process.exit(1)
  }
}

runDemo()
