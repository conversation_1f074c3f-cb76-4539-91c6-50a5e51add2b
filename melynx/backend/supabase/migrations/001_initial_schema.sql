-- MELynx Initial Database Schema
-- This migration creates the core tables for MEL compliance analysis

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users and Authentication (extends Supabase auth.users)
CREATE TABLE public.inspectors (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'inspector' CHECK (role IN ('admin', 'inspector', 'operator', 'viewer')),
    organization TEXT,
    license_number TEXT,
    certifications TEXT[],
    specializations TEXT[],
    approved_aircraft_types TEXT[],
    permissions TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- MEL Documents
CREATE TABLE public.mel_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    filename TEXT NOT NULL,
    original_name TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    size BIGINT NOT NULL,
    storage_path TEXT NOT NULL,
    uploaded_by UUID REFERENCES public.inspectors(id),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    parsed_at TIMESTAMP WITH TIME ZONE,
    status TEXT NOT NULL DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'parsing', 'parsed', 'failed', 'archived')),
    
    -- Document metadata
    aircraft_type TEXT,
    operator TEXT,
    effective_date DATE,
    revision_number TEXT,
    document_type TEXT NOT NULL CHECK (document_type IN ('operator_mel', 'master_mel', 'other')),
    page_count INTEGER,
    language TEXT DEFAULT 'en',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MEL Clauses extracted from documents
CREATE TABLE public.mel_clauses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID REFERENCES public.mel_documents(id) ON DELETE CASCADE,
    clause_number TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT NOT NULL,
    subcategory TEXT,
    conditions TEXT[],
    limitations TEXT[],
    maintenance_actions TEXT[],
    operational_procedures TEXT[],
    placard TEXT,
    remarks TEXT,
    page_number INTEGER,
    section TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique clause numbers within a document
    UNIQUE(document_id, clause_number)
);

-- MEL Analyses (comparison between operator and master MELs)
CREATE TABLE public.mel_evaluations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    operator_mel_id UUID REFERENCES public.mel_documents(id),
    master_mel_id UUID REFERENCES public.mel_documents(id),
    created_by UUID REFERENCES public.inspectors(id),
    status TEXT NOT NULL DEFAULT 'initiated' CHECK (status IN (
        'initiated', 'parsing_documents', 'extracting_clauses', 
        'comparing_clauses', 'generating_summary', 'completed', 'failed', 'cancelled'
    )),
    
    -- Analysis options
    clause_by_clause_comparison BOOLEAN DEFAULT true,
    compliance_gap_analysis BOOLEAN DEFAULT true,
    generate_detailed_report BOOLEAN DEFAULT true,
    enable_realtime_chat BOOLEAN DEFAULT false,
    include_recommendations BOOLEAN DEFAULT true,
    
    -- Results (stored as JSONB for flexibility)
    results JSONB,
    
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MEL Differences found during analysis
CREATE TABLE public.mel_differences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    evaluation_id UUID REFERENCES public.mel_evaluations(id) ON DELETE CASCADE,
    operator_clause_id UUID REFERENCES public.mel_clauses(id),
    master_clause_id UUID REFERENCES public.mel_clauses(id),
    
    comparison_status TEXT NOT NULL CHECK (comparison_status IN (
        'matched', 'missing', 'modified', 'additional', 'conflicting'
    )),
    
    issue_type TEXT NOT NULL CHECK (issue_type IN (
        'missing_clause', 'modified_clause', 'additional_clause', 
        'conflicting_requirements', 'formatting_issue', 'regulatory_compliance'
    )),
    
    severity TEXT NOT NULL DEFAULT 'medium' CHECK (severity IN ('critical', 'high', 'medium', 'low', 'info')),
    
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    differences TEXT[],
    recommendation TEXT,
    regulatory_reference TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat Sessions for Q&A
CREATE TABLE public.mel_chat_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    evaluation_id UUID REFERENCES public.mel_evaluations(id),
    created_by UUID REFERENCES public.inspectors(id),
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'ended', 'archived')),
    
    -- Context information
    context_type TEXT NOT NULL DEFAULT 'general' CHECK (context_type IN ('general', 'analysis_specific', 'clause_specific')),
    context_clause_id UUID REFERENCES public.mel_clauses(id),
    aircraft_type TEXT,
    regulations TEXT[],
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE
);

-- Chat Messages
CREATE TABLE public.mel_chat_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id UUID REFERENCES public.mel_chat_sessions(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    
    -- Metadata for AI responses
    metadata JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_mel_documents_uploaded_by ON public.mel_documents(uploaded_by);
CREATE INDEX idx_mel_documents_status ON public.mel_documents(status);
CREATE INDEX idx_mel_documents_document_type ON public.mel_documents(document_type);
CREATE INDEX idx_mel_documents_aircraft_type ON public.mel_documents(aircraft_type);

CREATE INDEX idx_mel_clauses_document_id ON public.mel_clauses(document_id);
CREATE INDEX idx_mel_clauses_category ON public.mel_clauses(category);
CREATE INDEX idx_mel_clauses_clause_number ON public.mel_clauses(clause_number);

CREATE INDEX idx_mel_evaluations_created_by ON public.mel_evaluations(created_by);
CREATE INDEX idx_mel_evaluations_status ON public.mel_evaluations(status);
CREATE INDEX idx_mel_evaluations_started_at ON public.mel_evaluations(started_at);

CREATE INDEX idx_mel_differences_evaluation_id ON public.mel_differences(evaluation_id);
CREATE INDEX idx_mel_differences_severity ON public.mel_differences(severity);
CREATE INDEX idx_mel_differences_issue_type ON public.mel_differences(issue_type);

CREATE INDEX idx_mel_chat_sessions_created_by ON public.mel_chat_sessions(created_by);
CREATE INDEX idx_mel_chat_sessions_evaluation_id ON public.mel_chat_sessions(evaluation_id);
CREATE INDEX idx_mel_chat_sessions_status ON public.mel_chat_sessions(status);

CREATE INDEX idx_mel_chat_logs_session_id ON public.mel_chat_logs(session_id);
CREATE INDEX idx_mel_chat_logs_created_at ON public.mel_chat_logs(created_at);

-- Row Level Security (RLS) Policies
ALTER TABLE public.inspectors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_clauses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_evaluations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_differences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_chat_logs ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (to be refined based on requirements)
CREATE POLICY "Users can view their own inspector profile" ON public.inspectors
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own inspector profile" ON public.inspectors
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view documents they uploaded" ON public.mel_documents
    FOR SELECT USING (auth.uid() = uploaded_by);

CREATE POLICY "Users can upload documents" ON public.mel_documents
    FOR INSERT WITH CHECK (auth.uid() = uploaded_by);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_inspectors_updated_at BEFORE UPDATE ON public.inspectors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mel_documents_updated_at BEFORE UPDATE ON public.mel_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mel_clauses_updated_at BEFORE UPDATE ON public.mel_clauses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mel_evaluations_updated_at BEFORE UPDATE ON public.mel_evaluations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mel_differences_updated_at BEFORE UPDATE ON public.mel_differences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
