#!/usr/bin/env node

import dotenv from 'dotenv'
dotenv.config()

console.log('🔧 Testing MELynx Backend Configuration...\n')

// Test environment variables
console.log('📋 Environment Variables:')
console.log('  SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ SET' : '❌ NOT SET')
console.log('  SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? '✅ SET' : '❌ NOT SET')
console.log('  OPENROUTER_API_KEY:', process.env.OPENROUTER_API_KEY ? '✅ SET' : '❌ NOT SET')

// Test Supabase connection
try {
  const { createClient } = await import('@supabase/supabase-js')
  
  const supabaseUrl = process.env.SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_ANON_KEY
  
  if (!supabaseUrl || !supabaseKey) {
    console.log('\n❌ Missing Supabase configuration')
    process.exit(1)
  }
  
  const supabase = createClient(supabaseUrl, supabaseKey)
  
  console.log('\n🔗 Testing Supabase Connection...')
  
  // Test basic connection
  const { data, error } = await supabase
    .from('mel_documents')
    .select('count')
    .limit(1)
  
  if (error) {
    console.log('⚠️  Supabase connection test failed:', error.message)
    console.log('💡 This is expected if the schema hasn\'t been applied yet')
  } else {
    console.log('✅ Supabase connection successful!')
  }
  
} catch (error) {
  console.log('❌ Connection test failed:', error.message)
}

console.log('\n🎯 Next Steps:')
console.log('1. Apply the Supabase schema from supabase_schema.sql')
console.log('2. Start the backend server: npm run dev')
console.log('3. Test the full system with document uploads')

console.log('\n🚀 MELynx Backend Configuration Test Complete!')
